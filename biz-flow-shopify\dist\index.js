// server/index.ts
import express2 from "express";

// server/routes.ts
import { createServer } from "http";

// shared/schema.ts
import { pgTable, text, serial, integer, boolean, timestamp, decimal, jsonb } from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
var users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  email: text("email").notNull().unique(),
  password: text("password").notNull(),
  firstName: text("first_name"),
  lastName: text("last_name"),
  avatar: text("avatar"),
  role: text("role").notNull().default("user"),
  isActive: boolean("is_active").notNull().default(true),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow()
});
var products = pgTable("products", {
  id: serial("id").primary<PERSON>ey(),
  name: text("name").notNull(),
  description: text("description"),
  category: text("category").notNull(),
  price: decimal("price", { precision: 10, scale: 2 }).notNull(),
  cost: decimal("cost", { precision: 10, scale: 2 }),
  stock: integer("stock").notNull().default(0),
  sku: text("sku").unique(),
  status: text("status").notNull().default("active"),
  images: text("images").array(),
  tags: text("tags").array(),
  metadata: jsonb("metadata"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow()
});
var customers = pgTable("customers", {
  id: serial("id").primaryKey(),
  firstName: text("first_name").notNull(),
  lastName: text("last_name").notNull(),
  email: text("email").notNull().unique(),
  phone: text("phone"),
  company: text("company"),
  address: jsonb("address"),
  tier: text("tier").notNull().default("standard"),
  totalSpent: decimal("total_spent", { precision: 10, scale: 2 }).notNull().default("0"),
  totalOrders: integer("total_orders").notNull().default(0),
  lastOrderDate: timestamp("last_order_date"),
  status: text("status").notNull().default("active"),
  notes: text("notes"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow()
});
var orders = pgTable("orders", {
  id: serial("id").primaryKey(),
  orderNumber: text("order_number").notNull().unique(),
  customerId: integer("customer_id").references(() => customers.id),
  status: text("status").notNull().default("pending"),
  paymentStatus: text("payment_status").notNull().default("pending"),
  paymentMethod: text("payment_method"),
  subtotal: decimal("subtotal", { precision: 10, scale: 2 }).notNull(),
  tax: decimal("tax", { precision: 10, scale: 2 }).notNull().default("0"),
  shipping: decimal("shipping", { precision: 10, scale: 2 }).notNull().default("0"),
  total: decimal("total", { precision: 10, scale: 2 }).notNull(),
  currency: text("currency").notNull().default("USD"),
  shippingAddress: jsonb("shipping_address"),
  billingAddress: jsonb("billing_address"),
  notes: text("notes"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow()
});
var orderItems = pgTable("order_items", {
  id: serial("id").primaryKey(),
  orderId: integer("order_id").references(() => orders.id),
  productId: integer("product_id").references(() => products.id),
  quantity: integer("quantity").notNull(),
  price: decimal("price", { precision: 10, scale: 2 }).notNull(),
  total: decimal("total", { precision: 10, scale: 2 }).notNull()
});
var analytics = pgTable("analytics", {
  id: serial("id").primaryKey(),
  metric: text("metric").notNull(),
  value: decimal("value", { precision: 15, scale: 2 }).notNull(),
  period: text("period").notNull(),
  date: timestamp("date").notNull(),
  metadata: jsonb("metadata")
});
var supportTickets = pgTable("support_tickets", {
  id: serial("id").primaryKey(),
  ticketNumber: text("ticket_number").notNull().unique(),
  customerId: integer("customer_id").references(() => customers.id),
  subject: text("subject").notNull(),
  description: text("description").notNull(),
  priority: text("priority").notNull().default("medium"),
  status: text("status").notNull().default("open"),
  category: text("category").notNull(),
  assignedTo: integer("assigned_to").references(() => users.id),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow()
});
var insertUserSchema = createInsertSchema(users).omit({ id: true, createdAt: true, updatedAt: true });
var selectUserSchema = createSelectSchema(users);
var updateUserSchema = insertUserSchema.partial();
var insertProductSchema = createInsertSchema(products).omit({ id: true, createdAt: true, updatedAt: true });
var selectProductSchema = createSelectSchema(products);
var updateProductSchema = insertProductSchema.partial();
var insertCustomerSchema = createInsertSchema(customers).omit({ id: true, createdAt: true, updatedAt: true });
var selectCustomerSchema = createSelectSchema(customers);
var updateCustomerSchema = insertCustomerSchema.partial();
var insertOrderSchema = createInsertSchema(orders).omit({ id: true, createdAt: true, updatedAt: true });
var selectOrderSchema = createSelectSchema(orders);
var updateOrderSchema = insertOrderSchema.partial();
var insertOrderItemSchema = createInsertSchema(orderItems).omit({ id: true });
var selectOrderItemSchema = createSelectSchema(orderItems);
var insertSupportTicketSchema = createInsertSchema(supportTickets).omit({ id: true, createdAt: true, updatedAt: true });
var selectSupportTicketSchema = createSelectSchema(supportTickets);
var updateSupportTicketSchema = insertSupportTicketSchema.partial();
var PRODUCT_STATUS = {
  ACTIVE: "active",
  INACTIVE: "inactive",
  DISCONTINUED: "discontinued"
};
var ORDER_STATUS = {
  PENDING: "pending",
  PROCESSING: "processing",
  SHIPPED: "shipped",
  DELIVERED: "delivered",
  CANCELLED: "cancelled"
};
var PAYMENT_STATUS = {
  PENDING: "pending",
  PAID: "paid",
  FAILED: "failed",
  REFUNDED: "refunded"
};
var CUSTOMER_TIERS = {
  STANDARD: "standard",
  PREMIUM: "premium",
  VIP: "vip"
};
var SUPPORT_STATUS = {
  OPEN: "open",
  IN_PROGRESS: "in_progress",
  RESOLVED: "resolved",
  CLOSED: "closed"
};

// server/storage.ts
var MemStorage = class {
  users;
  products;
  customers;
  orders;
  orderItems;
  supportTickets;
  analytics;
  currentUserId;
  currentProductId;
  currentCustomerId;
  currentOrderId;
  currentOrderItemId;
  currentTicketId;
  constructor() {
    this.users = /* @__PURE__ */ new Map();
    this.products = /* @__PURE__ */ new Map();
    this.customers = /* @__PURE__ */ new Map();
    this.orders = /* @__PURE__ */ new Map();
    this.orderItems = /* @__PURE__ */ new Map();
    this.supportTickets = /* @__PURE__ */ new Map();
    this.analytics = /* @__PURE__ */ new Map();
    this.currentUserId = 1;
    this.currentProductId = 1;
    this.currentCustomerId = 1;
    this.currentOrderId = 1;
    this.currentOrderItemId = 1;
    this.currentTicketId = 1;
    this.initializeSampleData();
  }
  initializeSampleData() {
    const sampleUsers = [
      {
        id: 1,
        username: "admin",
        email: "<EMAIL>",
        password: "admin123",
        firstName: "Admin",
        lastName: "User",
        role: "admin",
        isActive: true,
        avatar: null,
        createdAt: /* @__PURE__ */ new Date(),
        updatedAt: /* @__PURE__ */ new Date()
      }
    ];
    const sampleCustomers = [
      {
        id: 1,
        firstName: "Alice",
        lastName: "Johnson",
        email: "<EMAIL>",
        phone: "+****************",
        company: "Tech Solutions Inc.",
        address: { street: "123 Main St", city: "San Francisco", state: "CA", zip: "94105" },
        tier: CUSTOMER_TIERS.VIP,
        totalSpent: "2847.50",
        totalOrders: 24,
        lastOrderDate: /* @__PURE__ */ new Date(),
        status: "active",
        notes: "VIP customer with excellent purchase history",
        createdAt: /* @__PURE__ */ new Date(),
        updatedAt: /* @__PURE__ */ new Date()
      },
      {
        id: 2,
        firstName: "Bob",
        lastName: "Smith",
        email: "<EMAIL>",
        phone: "+****************",
        company: "Design Studio",
        address: { street: "456 Oak Ave", city: "New York", state: "NY", zip: "10001" },
        tier: CUSTOMER_TIERS.PREMIUM,
        totalSpent: "1456.80",
        totalOrders: 15,
        lastOrderDate: /* @__PURE__ */ new Date(),
        status: "active",
        notes: "Regular customer, prefers e-commerce solutions",
        createdAt: /* @__PURE__ */ new Date(),
        updatedAt: /* @__PURE__ */ new Date()
      }
    ];
    const sampleProducts = [
      {
        id: 1,
        name: "Premium Dashboard Template",
        description: "Modern, responsive dashboard template with advanced features",
        category: "Templates",
        price: "89.00",
        cost: "25.00",
        stock: 45,
        sku: "DASH-001",
        status: PRODUCT_STATUS.ACTIVE,
        images: ["/images/dashboard-template.jpg"],
        tags: ["dashboard", "template", "premium"],
        metadata: { featured: true, downloads: 234 },
        createdAt: /* @__PURE__ */ new Date(),
        updatedAt: /* @__PURE__ */ new Date()
      },
      {
        id: 2,
        name: "E-commerce Starter Kit",
        description: "Complete e-commerce solution with payment integration",
        category: "Kits",
        price: "156.00",
        cost: "40.00",
        stock: 23,
        sku: "ECOM-001",
        status: PRODUCT_STATUS.ACTIVE,
        images: ["/images/ecommerce-kit.jpg"],
        tags: ["ecommerce", "starter", "kit"],
        metadata: { featured: true, downloads: 156 },
        createdAt: /* @__PURE__ */ new Date(),
        updatedAt: /* @__PURE__ */ new Date()
      }
    ];
    const sampleOrders = [
      {
        id: 1,
        orderNumber: "ORD-2024-001",
        customerId: 1,
        status: ORDER_STATUS.DELIVERED,
        paymentStatus: PAYMENT_STATUS.PAID,
        paymentMethod: "Credit Card",
        subtotal: "118.00",
        tax: "0.00",
        shipping: "0.00",
        total: "118.00",
        currency: "USD",
        shippingAddress: { street: "123 Main St", city: "San Francisco", state: "CA", zip: "94105" },
        billingAddress: { street: "123 Main St", city: "San Francisco", state: "CA", zip: "94105" },
        notes: "Digital download order",
        createdAt: /* @__PURE__ */ new Date(),
        updatedAt: /* @__PURE__ */ new Date()
      }
    ];
    const sampleTickets = [
      {
        id: 1,
        ticketNumber: "TK-2024-001",
        customerId: 1,
        subject: "Payment processing issue",
        description: "Customer experiencing issues with payment processing",
        priority: "high",
        status: SUPPORT_STATUS.OPEN,
        category: "billing",
        assignedTo: 1,
        createdAt: /* @__PURE__ */ new Date(),
        updatedAt: /* @__PURE__ */ new Date()
      }
    ];
    sampleUsers.forEach((user) => this.users.set(user.id, user));
    sampleCustomers.forEach((customer) => this.customers.set(customer.id, customer));
    sampleProducts.forEach((product) => this.products.set(product.id, product));
    sampleOrders.forEach((order) => this.orders.set(order.id, order));
    sampleTickets.forEach((ticket) => this.supportTickets.set(ticket.id, ticket));
    this.currentUserId = Math.max(...Array.from(this.users.keys())) + 1;
    this.currentCustomerId = Math.max(...Array.from(this.customers.keys())) + 1;
    this.currentProductId = Math.max(...Array.from(this.products.keys())) + 1;
    this.currentOrderId = Math.max(...Array.from(this.orders.keys())) + 1;
    this.currentTicketId = Math.max(...Array.from(this.supportTickets.keys())) + 1;
  }
  // User operations
  async getUser(id) {
    return this.users.get(id);
  }
  async getUserByUsername(username) {
    return Array.from(this.users.values()).find((user) => user.username === username);
  }
  async getUserByEmail(email) {
    return Array.from(this.users.values()).find((user) => user.email === email);
  }
  async createUser(insertUser) {
    const id = this.currentUserId++;
    const user = {
      ...insertUser,
      id,
      createdAt: /* @__PURE__ */ new Date(),
      updatedAt: /* @__PURE__ */ new Date()
    };
    this.users.set(id, user);
    return user;
  }
  async updateUser(id, updates) {
    const user = this.users.get(id);
    if (!user) return void 0;
    const updatedUser = { ...user, ...updates, updatedAt: /* @__PURE__ */ new Date() };
    this.users.set(id, updatedUser);
    return updatedUser;
  }
  async deleteUser(id) {
    return this.users.delete(id);
  }
  // Product operations
  async getProduct(id) {
    return this.products.get(id);
  }
  async getProducts(filters) {
    let products3 = Array.from(this.products.values());
    if (filters?.category) {
      products3 = products3.filter((p) => p.category === filters.category);
    }
    if (filters?.status) {
      products3 = products3.filter((p) => p.status === filters.status);
    }
    return products3;
  }
  async createProduct(insertProduct) {
    const id = this.currentProductId++;
    const product = {
      ...insertProduct,
      id,
      createdAt: /* @__PURE__ */ new Date(),
      updatedAt: /* @__PURE__ */ new Date()
    };
    this.products.set(id, product);
    return product;
  }
  async updateProduct(id, updates) {
    const product = this.products.get(id);
    if (!product) return void 0;
    const updatedProduct = { ...product, ...updates, updatedAt: /* @__PURE__ */ new Date() };
    this.products.set(id, updatedProduct);
    return updatedProduct;
  }
  async deleteProduct(id) {
    return this.products.delete(id);
  }
  // Customer operations
  async getCustomer(id) {
    return this.customers.get(id);
  }
  async getCustomerByEmail(email) {
    return Array.from(this.customers.values()).find((customer) => customer.email === email);
  }
  async getCustomers(filters) {
    let customers3 = Array.from(this.customers.values());
    if (filters?.tier) {
      customers3 = customers3.filter((c) => c.tier === filters.tier);
    }
    if (filters?.status) {
      customers3 = customers3.filter((c) => c.status === filters.status);
    }
    return customers3;
  }
  async createCustomer(insertCustomer) {
    const id = this.currentCustomerId++;
    const customer = {
      ...insertCustomer,
      id,
      createdAt: /* @__PURE__ */ new Date(),
      updatedAt: /* @__PURE__ */ new Date()
    };
    this.customers.set(id, customer);
    return customer;
  }
  async updateCustomer(id, updates) {
    const customer = this.customers.get(id);
    if (!customer) return void 0;
    const updatedCustomer = { ...customer, ...updates, updatedAt: /* @__PURE__ */ new Date() };
    this.customers.set(id, updatedCustomer);
    return updatedCustomer;
  }
  async deleteCustomer(id) {
    return this.customers.delete(id);
  }
  // Order operations
  async getOrder(id) {
    return this.orders.get(id);
  }
  async getOrderByNumber(orderNumber) {
    return Array.from(this.orders.values()).find((order) => order.orderNumber === orderNumber);
  }
  async getOrders(filters) {
    let orders3 = Array.from(this.orders.values());
    if (filters?.customerId) {
      orders3 = orders3.filter((o) => o.customerId === filters.customerId);
    }
    if (filters?.status) {
      orders3 = orders3.filter((o) => o.status === filters.status);
    }
    return orders3;
  }
  async createOrder(insertOrder) {
    const id = this.currentOrderId++;
    const order = {
      ...insertOrder,
      id,
      createdAt: /* @__PURE__ */ new Date(),
      updatedAt: /* @__PURE__ */ new Date()
    };
    this.orders.set(id, order);
    return order;
  }
  async updateOrder(id, updates) {
    const order = this.orders.get(id);
    if (!order) return void 0;
    const updatedOrder = { ...order, ...updates, updatedAt: /* @__PURE__ */ new Date() };
    this.orders.set(id, updatedOrder);
    return updatedOrder;
  }
  async deleteOrder(id) {
    return this.orders.delete(id);
  }
  // Order item operations
  async getOrderItems(orderId) {
    return Array.from(this.orderItems.values()).filter((item) => item.orderId === orderId);
  }
  async addOrderItem(insertItem) {
    const id = this.currentOrderItemId++;
    const item = { ...insertItem, id };
    this.orderItems.set(id, item);
    return item;
  }
  async updateOrderItem(id, updates) {
    const item = this.orderItems.get(id);
    if (!item) return void 0;
    const updatedItem = { ...item, ...updates };
    this.orderItems.set(id, updatedItem);
    return updatedItem;
  }
  async deleteOrderItem(id) {
    return this.orderItems.delete(id);
  }
  // Support ticket operations
  async getSupportTicket(id) {
    return this.supportTickets.get(id);
  }
  async getSupportTickets(filters) {
    let tickets = Array.from(this.supportTickets.values());
    if (filters?.customerId) {
      tickets = tickets.filter((t) => t.customerId === filters.customerId);
    }
    if (filters?.status) {
      tickets = tickets.filter((t) => t.status === filters.status);
    }
    return tickets;
  }
  async createSupportTicket(insertTicket) {
    const id = this.currentTicketId++;
    const ticket = {
      ...insertTicket,
      id,
      createdAt: /* @__PURE__ */ new Date(),
      updatedAt: /* @__PURE__ */ new Date()
    };
    this.supportTickets.set(id, ticket);
    return ticket;
  }
  async updateSupportTicket(id, updates) {
    const ticket = this.supportTickets.get(id);
    if (!ticket) return void 0;
    const updatedTicket = { ...ticket, ...updates, updatedAt: /* @__PURE__ */ new Date() };
    this.supportTickets.set(id, updatedTicket);
    return updatedTicket;
  }
  async deleteSupportTicket(id) {
    return this.supportTickets.delete(id);
  }
  // Analytics operations
  async getAnalytics(metric, period) {
    const key = `${metric}-${period}`;
    return this.analytics.get(key) || [];
  }
  async recordAnalytic(metric, value, period, metadata) {
    const key = `${metric}-${period}`;
    const existing = this.analytics.get(key) || [];
    existing.push({ value, date: /* @__PURE__ */ new Date(), metadata });
    this.analytics.set(key, existing);
  }
};
var storage = new MemStorage();

// server/routes.ts
import { z } from "zod";
var asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};
var sendSuccess = (res, data, message) => {
  res.json({ success: true, data, message });
};
var sendError = (res, message, status = 400) => {
  res.status(status).json({ success: false, error: message });
};
var validateSchema = (schema) => (req, res, next) => {
  try {
    req.validatedData = schema.parse(req.body);
    next();
  } catch (error) {
    if (error instanceof z.ZodError) {
      return sendError(res, `Validation error: ${error.errors.map((e) => e.message).join(", ")}`);
    }
    return sendError(res, "Invalid request data");
  }
};
async function registerRoutes(app2) {
  app2.get("/api/health", (req, res) => {
    sendSuccess(res, { status: "healthy", timestamp: /* @__PURE__ */ new Date() });
  });
  app2.get("/api/users/:id", asyncHandler(async (req, res) => {
    const user = await storage.getUser(parseInt(req.params.id));
    if (!user) return sendError(res, "User not found", 404);
    sendSuccess(res, user);
  }));
  app2.get("/api/users/username/:username", asyncHandler(async (req, res) => {
    const user = await storage.getUserByUsername(req.params.username);
    if (!user) return sendError(res, "User not found", 404);
    sendSuccess(res, user);
  }));
  app2.post("/api/users", validateSchema(insertUserSchema), asyncHandler(async (req, res) => {
    const user = await storage.createUser(req.validatedData);
    sendSuccess(res, user, "User created successfully");
  }));
  app2.put("/api/users/:id", validateSchema(updateUserSchema), asyncHandler(async (req, res) => {
    const user = await storage.updateUser(parseInt(req.params.id), req.validatedData);
    if (!user) return sendError(res, "User not found", 404);
    sendSuccess(res, user, "User updated successfully");
  }));
  app2.delete("/api/users/:id", asyncHandler(async (req, res) => {
    const deleted = await storage.deleteUser(parseInt(req.params.id));
    if (!deleted) return sendError(res, "User not found", 404);
    sendSuccess(res, null, "User deleted successfully");
  }));
  app2.get("/api/products", asyncHandler(async (req, res) => {
    const { category, status } = req.query;
    const products3 = await storage.getProducts({ category, status });
    sendSuccess(res, products3);
  }));
  app2.get("/api/products/:id", asyncHandler(async (req, res) => {
    const product = await storage.getProduct(parseInt(req.params.id));
    if (!product) return sendError(res, "Product not found", 404);
    sendSuccess(res, product);
  }));
  app2.post("/api/products", validateSchema(insertProductSchema), asyncHandler(async (req, res) => {
    const product = await storage.createProduct(req.validatedData);
    sendSuccess(res, product, "Product created successfully");
  }));
  app2.put("/api/products/:id", validateSchema(updateProductSchema), asyncHandler(async (req, res) => {
    const product = await storage.updateProduct(parseInt(req.params.id), req.validatedData);
    if (!product) return sendError(res, "Product not found", 404);
    sendSuccess(res, product, "Product updated successfully");
  }));
  app2.delete("/api/products/:id", asyncHandler(async (req, res) => {
    const deleted = await storage.deleteProduct(parseInt(req.params.id));
    if (!deleted) return sendError(res, "Product not found", 404);
    sendSuccess(res, null, "Product deleted successfully");
  }));
  app2.get("/api/customers", asyncHandler(async (req, res) => {
    const { tier, status } = req.query;
    const customers3 = await storage.getCustomers({ tier, status });
    sendSuccess(res, customers3);
  }));
  app2.get("/api/customers/:id", asyncHandler(async (req, res) => {
    const customer = await storage.getCustomer(parseInt(req.params.id));
    if (!customer) return sendError(res, "Customer not found", 404);
    sendSuccess(res, customer);
  }));
  app2.get("/api/customers/email/:email", asyncHandler(async (req, res) => {
    const customer = await storage.getCustomerByEmail(req.params.email);
    if (!customer) return sendError(res, "Customer not found", 404);
    sendSuccess(res, customer);
  }));
  app2.post("/api/customers", validateSchema(insertCustomerSchema), asyncHandler(async (req, res) => {
    const customer = await storage.createCustomer(req.validatedData);
    sendSuccess(res, customer, "Customer created successfully");
  }));
  app2.put("/api/customers/:id", validateSchema(updateCustomerSchema), asyncHandler(async (req, res) => {
    const customer = await storage.updateCustomer(parseInt(req.params.id), req.validatedData);
    if (!customer) return sendError(res, "Customer not found", 404);
    sendSuccess(res, customer, "Customer updated successfully");
  }));
  app2.delete("/api/customers/:id", asyncHandler(async (req, res) => {
    const deleted = await storage.deleteCustomer(parseInt(req.params.id));
    if (!deleted) return sendError(res, "Customer not found", 404);
    sendSuccess(res, null, "Customer deleted successfully");
  }));
  app2.get("/api/orders", asyncHandler(async (req, res) => {
    const { customerId, status } = req.query;
    const orders3 = await storage.getOrders({
      customerId: customerId ? parseInt(customerId) : void 0,
      status
    });
    sendSuccess(res, orders3);
  }));
  app2.get("/api/orders/:id", asyncHandler(async (req, res) => {
    const order = await storage.getOrder(parseInt(req.params.id));
    if (!order) return sendError(res, "Order not found", 404);
    sendSuccess(res, order);
  }));
  app2.get("/api/orders/number/:orderNumber", asyncHandler(async (req, res) => {
    const order = await storage.getOrderByNumber(req.params.orderNumber);
    if (!order) return sendError(res, "Order not found", 404);
    sendSuccess(res, order);
  }));
  app2.post("/api/orders", validateSchema(insertOrderSchema), asyncHandler(async (req, res) => {
    const order = await storage.createOrder(req.validatedData);
    sendSuccess(res, order, "Order created successfully");
  }));
  app2.put("/api/orders/:id", validateSchema(updateOrderSchema), asyncHandler(async (req, res) => {
    const order = await storage.updateOrder(parseInt(req.params.id), req.validatedData);
    if (!order) return sendError(res, "Order not found", 404);
    sendSuccess(res, order, "Order updated successfully");
  }));
  app2.delete("/api/orders/:id", asyncHandler(async (req, res) => {
    const deleted = await storage.deleteOrder(parseInt(req.params.id));
    if (!deleted) return sendError(res, "Order not found", 404);
    sendSuccess(res, null, "Order deleted successfully");
  }));
  app2.get("/api/orders/:orderId/items", asyncHandler(async (req, res) => {
    const items = await storage.getOrderItems(parseInt(req.params.orderId));
    sendSuccess(res, items);
  }));
  app2.post("/api/orders/:orderId/items", validateSchema(insertOrderItemSchema), asyncHandler(async (req, res) => {
    const item = await storage.addOrderItem({
      ...req.validatedData,
      orderId: parseInt(req.params.orderId)
    });
    sendSuccess(res, item, "Order item added successfully");
  }));
  app2.put("/api/order-items/:id", asyncHandler(async (req, res) => {
    const item = await storage.updateOrderItem(parseInt(req.params.id), req.body);
    if (!item) return sendError(res, "Order item not found", 404);
    sendSuccess(res, item, "Order item updated successfully");
  }));
  app2.delete("/api/order-items/:id", asyncHandler(async (req, res) => {
    const deleted = await storage.deleteOrderItem(parseInt(req.params.id));
    if (!deleted) return sendError(res, "Order item not found", 404);
    sendSuccess(res, null, "Order item deleted successfully");
  }));
  app2.get("/api/support-tickets", asyncHandler(async (req, res) => {
    const { customerId, status } = req.query;
    const tickets = await storage.getSupportTickets({
      customerId: customerId ? parseInt(customerId) : void 0,
      status
    });
    sendSuccess(res, tickets);
  }));
  app2.get("/api/support-tickets/:id", asyncHandler(async (req, res) => {
    const ticket = await storage.getSupportTicket(parseInt(req.params.id));
    if (!ticket) return sendError(res, "Support ticket not found", 404);
    sendSuccess(res, ticket);
  }));
  app2.post("/api/support-tickets", validateSchema(insertSupportTicketSchema), asyncHandler(async (req, res) => {
    const ticket = await storage.createSupportTicket(req.validatedData);
    sendSuccess(res, ticket, "Support ticket created successfully");
  }));
  app2.put("/api/support-tickets/:id", validateSchema(updateSupportTicketSchema), asyncHandler(async (req, res) => {
    const ticket = await storage.updateSupportTicket(parseInt(req.params.id), req.validatedData);
    if (!ticket) return sendError(res, "Support ticket not found", 404);
    sendSuccess(res, ticket, "Support ticket updated successfully");
  }));
  app2.delete("/api/support-tickets/:id", asyncHandler(async (req, res) => {
    const deleted = await storage.deleteSupportTicket(parseInt(req.params.id));
    if (!deleted) return sendError(res, "Support ticket not found", 404);
    sendSuccess(res, null, "Support ticket deleted successfully");
  }));
  app2.get("/api/analytics/:metric/:period", asyncHandler(async (req, res) => {
    const { metric, period } = req.params;
    const analytics3 = await storage.getAnalytics(metric, period);
    sendSuccess(res, analytics3);
  }));
  app2.post("/api/analytics", asyncHandler(async (req, res) => {
    const { metric, value, period, metadata } = req.body;
    if (!metric || !value || !period) {
      return sendError(res, "Missing required fields: metric, value, period");
    }
    await storage.recordAnalytic(metric, parseFloat(value), period, metadata);
    sendSuccess(res, null, "Analytics recorded successfully");
  }));
  app2.get("/api/dashboard/stats", asyncHandler(async (req, res) => {
    const customers3 = await storage.getCustomers();
    const products3 = await storage.getProducts();
    const orders3 = await storage.getOrders();
    const tickets = await storage.getSupportTickets();
    const stats = {
      totalCustomers: customers3.length,
      totalProducts: products3.length,
      totalOrders: orders3.length,
      totalRevenue: orders3.reduce((sum, order) => sum + parseFloat(order.total), 0),
      openTickets: tickets.filter((t) => t.status === "open").length,
      customersByTier: customers3.reduce((acc, customer) => {
        acc[customer.tier] = (acc[customer.tier] || 0) + 1;
        return acc;
      }, {}),
      ordersByStatus: orders3.reduce((acc, order) => {
        acc[order.status] = (acc[order.status] || 0) + 1;
        return acc;
      }, {})
    };
    sendSuccess(res, stats);
  }));
  app2.get("/api/dashboard/recent-activity", asyncHandler(async (req, res) => {
    const orders3 = await storage.getOrders();
    const customers3 = await storage.getCustomers();
    const tickets = await storage.getSupportTickets();
    const recentOrders = orders3.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()).slice(0, 5);
    const recentCustomers = customers3.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()).slice(0, 5);
    const recentTickets = tickets.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()).slice(0, 5);
    const activity = {
      recentOrders,
      recentCustomers,
      recentTickets
    };
    sendSuccess(res, activity);
  }));
  const httpServer = createServer(app2);
  return httpServer;
}

// server/vite.ts
import express from "express";
import fs from "fs";
import path2 from "path";
import { createServer as createViteServer, createLogger } from "vite";

// vite.config.ts
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
import runtimeErrorOverlay from "@replit/vite-plugin-runtime-error-modal";
var vite_config_default = defineConfig({
  plugins: [
    react(),
    runtimeErrorOverlay(),
    ...process.env.NODE_ENV !== "production" && process.env.REPL_ID !== void 0 ? [
      await import("@replit/vite-plugin-cartographer").then(
        (m) => m.cartographer()
      )
    ] : []
  ],
  resolve: {
    alias: {
      "@": path.resolve(import.meta.dirname, "client", "src"),
      "@shared": path.resolve(import.meta.dirname, "shared"),
      "@assets": path.resolve(import.meta.dirname, "attached_assets")
    }
  },
  root: path.resolve(import.meta.dirname, "client"),
  build: {
    outDir: path.resolve(import.meta.dirname, "dist/public"),
    emptyOutDir: true
  }
});

// server/vite.ts
import { nanoid } from "nanoid";
var viteLogger = createLogger();
function log(message, source = "express") {
  const formattedTime = (/* @__PURE__ */ new Date()).toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true
  });
  console.log(`${formattedTime} [${source}] ${message}`);
}
async function setupVite(app2, server) {
  const serverOptions = {
    middlewareMode: true,
    hmr: { server },
    allowedHosts: true
  };
  const vite = await createViteServer({
    ...vite_config_default,
    configFile: false,
    customLogger: {
      ...viteLogger,
      error: (msg, options) => {
        viteLogger.error(msg, options);
        process.exit(1);
      }
    },
    server: serverOptions,
    appType: "custom"
  });
  app2.use(vite.middlewares);
  app2.use("*", async (req, res, next) => {
    const url = req.originalUrl;
    try {
      const clientTemplate = path2.resolve(
        import.meta.dirname,
        "..",
        "client",
        "index.html"
      );
      let template = await fs.promises.readFile(clientTemplate, "utf-8");
      template = template.replace(
        `src="/src/main.tsx"`,
        `src="/src/main.tsx?v=${nanoid()}"`
      );
      const page = await vite.transformIndexHtml(url, template);
      res.status(200).set({ "Content-Type": "text/html" }).end(page);
    } catch (e) {
      vite.ssrFixStacktrace(e);
      next(e);
    }
  });
}
function serveStatic(app2) {
  const distPath = path2.resolve(import.meta.dirname, "public");
  if (!fs.existsSync(distPath)) {
    throw new Error(
      `Could not find the build directory: ${distPath}, make sure to build the client first`
    );
  }
  app2.use(express.static(distPath));
  app2.use("*", (_req, res) => {
    res.sendFile(path2.resolve(distPath, "index.html"));
  });
}

// server/index.ts
var app = express2();
app.use(express2.json());
app.use(express2.urlencoded({ extended: false }));
app.use((req, res, next) => {
  const start = Date.now();
  const path3 = req.path;
  let capturedJsonResponse = void 0;
  const originalResJson = res.json;
  res.json = function(bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };
  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path3.startsWith("/api")) {
      let logLine = `${req.method} ${path3} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }
      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "\u2026";
      }
      log(logLine);
    }
  });
  next();
});
(async () => {
  const server = await registerRoutes(app);
  app.use((err, _req, res, _next) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";
    res.status(status).json({ message });
    throw err;
  });
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }
  const port = 5e3;
  server.listen(port, "localhost", () => {
    log(`serving on port ${port}`);
  });
})();
