import { Layout } from "@/components/Layout";
import { PageContainer } from "@/components/layout/PageContainer";
import { PageHeader } from "@/components/layout/PageHeader";
import { MetricCard } from "@/components/dashboard/MetricCard";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Filter, 
  Download, 
  RefreshCw,
  TrendingUp,
  TrendingDown,
  BarChart3,
  Users,
  DollarSign,
  ShoppingCart
} from "lucide-react";

export default function Analytics() {
  return (
    <Layout>
      <PageContainer>
        <PageHeader 
          title="Analytics & Insights"
          description="Comprehensive business analytics and performance metrics"
          actions={
            <div className="flex gap-2">
              <button className="glass-button px-4 py-2 flex items-center gap-2 text-sm">
                <Filter className="w-4 h-4" />
                Filter
              </button>
              <button className="glass-button px-4 py-2 flex items-center gap-2 text-sm">
                <Download className="w-4 h-4" />
                Export
              </button>
              <button className="glass-button px-4 py-2 flex items-center gap-2 text-sm">
                <RefreshCw className="w-4 h-4" />
                Refresh
              </button>
            </div>
          }
        />
        
        <div className="dashboard-section">
          <div className="stats-grid">
            <MetricCard
              title="Total Revenue"
              value="$45,231"
              change="+12.5%"
              changeType="positive"
              icon={<BarChart3 className="w-5 h-5" />}
            />
            
            <MetricCard
              title="Active Users"
              value="1,234"
              change="+8.2%"
              changeType="positive"
              icon={<Users className="w-5 h-5" />}
            />
            
            <MetricCard
              title="Conversion Rate"
              value="3.4%"
              change="-0.3%"
              changeType="negative"
              icon={<ShoppingCart className="w-5 h-5" />}
            />
            
            <MetricCard
              title="Avg. Order Value"
              value="$156.80"
              change="+15.3%"
              changeType="positive"
              icon={<DollarSign className="w-5 h-5" />}
            />
          </div>
          
          <div className="content-grid">
            <Card className="metric-card lg:col-span-2">
              <CardHeader>
                <CardTitle>Revenue Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center text-muted-foreground">
                  <div className="text-center">
                    <BarChart3 className="w-12 h-12 mx-auto mb-4" />
                    <p>Revenue analytics charts will be displayed here</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="metric-card">
              <CardHeader>
                <CardTitle>Performance Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Page Views</span>
                    <span className="font-medium">12,345</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Bounce Rate</span>
                    <span className="font-medium">32.1%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Session Duration</span>
                    <span className="font-medium">4m 23s</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </PageContainer>
    </Layout>
  );
}