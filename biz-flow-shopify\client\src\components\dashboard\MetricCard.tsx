import { ReactNode } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: string;
  changeType?: "positive" | "negative" | "neutral";
  icon?: ReactNode;
  description?: string;
  className?: string;
}

export function MetricCard({
  title,
  value,
  change,
  changeType = "neutral",
  icon,
  description,
  className
}: MetricCardProps) {
  const changeColors = {
    positive: "text-green-400",
    negative: "text-red-400", 
    neutral: "text-muted-foreground"
  };

  return (
    <div className={cn("metric-card group", className)}>
      <div className="p-6">
        <div className="flex items-center justify-between">
          <div className="space-y-3">
            <p className="text-sm font-medium text-foreground/60">{title}</p>
            <p className="text-3xl font-bold text-foreground group-hover:text-primary transition-colors duration-300">{value}</p>
            {(change || description) && (
              <div className="flex items-center gap-2 text-xs">
                {change && (
                  <span className={changeColors[changeType]}>{change}</span>
                )}
                {description && (
                  <span className="text-foreground/50">{description}</span>
                )}
              </div>
            )}
          </div>
          {icon && (
            <div className="p-3 bg-primary/10 rounded-xl backdrop-blur-sm border border-primary/20 group-hover:bg-primary/20 transition-all duration-300">
              <div className="w-6 h-6 text-primary">
                {icon}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}