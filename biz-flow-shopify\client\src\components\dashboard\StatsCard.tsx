import { ReactNode } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface StatsCardProps {
  title: string;
  value: string;
  change: string;
  changeType: "positive" | "negative" | "neutral";
  icon: ReactNode;
  description?: string;
  className?: string;
}

export function StatsCard({
  title,
  value,
  change,
  changeType,
  icon,
  description,
  className
}: StatsCardProps) {
  const changeColors = {
    positive: "text-success bg-success-light border-success/20",
    negative: "text-destructive bg-destructive-light border-destructive/20",
    neutral: "text-muted-foreground bg-muted border-border"
  };

  return (
    <Card className={cn(
      "metric-card group hover:shadow-lg transition-all duration-300 border-border/40 hover:border-primary/20 bg-gradient-to-br from-card to-card/80",
      className
    )}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="space-y-3">
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">{title}</p>
              <p className="text-2xl font-bold tracking-tight text-foreground neon-text">{value}</p>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge className={cn("text-xs font-medium border", changeColors[changeType])}>
                {change}
              </Badge>
              {description && (
                <span className="text-xs text-muted-foreground">{description}</span>
              )}
            </div>
          </div>
          
          <div className="p-3 bg-primary/10 rounded-xl group-hover:bg-primary/20 transition-all duration-300 group-hover:shadow-neon">
            <div className="w-6 h-6 text-primary group-hover:text-primary/90 transition-colors duration-300">
              {icon}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}