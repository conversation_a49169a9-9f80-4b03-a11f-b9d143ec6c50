import { 
  LayoutDashboard, 
  Package, 
  Users, 
  ShoppingCart, 
  BarChart3, 
  Settings,
  Code,
  MessageSquare,
  HelpCircle,
  Store,
  Sparkles,
  Eye
} from "lucide-react";
import { Link, useLocation } from "wouter";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";

const buildItems = [
  { title: "Dashboard", url: "/", icon: LayoutDashboard },
  { title: "AI App Builder", url: "/website-builder", icon: Sparkles },
];

const sellItems = [
  { title: "Products", url: "/products", icon: Package },
  { title: "Orders", url: "/orders", icon: ShoppingCart },
  { title: "Customers", url: "/customers", icon: Users },
  { title: "Analytics", url: "/analytics", icon: BarChart3 },
];

const developItems = [
  { title: "Development", url: "/development", icon: Code },
  { title: "API Keys", url: "/api-keys", icon: Package },
  { title: "Accessibility", url: "/accessibility", icon: Eye },
];

const supportItems = [
  { title: "Messages", url: "/messages", icon: MessageSquare },
  { title: "Support Center", url: "/support", icon: HelpCircle },
  { title: "Settings", url: "/settings", icon: Settings },
];

export function AppSidebar() {
  const { state } = useSidebar();
  const collapsed = state === "collapsed";
  const [location] = useLocation();
  const currentPath = location;

  const isActive = (path: string) => currentPath === path;
  const getNavCls = (path: string) =>
    isActive(path) 
      ? "bg-primary/20 text-primary font-medium rounded-xl border border-primary/30 backdrop-blur-sm" 
      : "text-foreground/70 hover:bg-primary/10 hover:text-primary transition-all duration-300 rounded-xl hover:backdrop-blur-sm";

  const renderNavGroup = (title: string, items: typeof buildItems) => (
    <SidebarGroup className="px-3 mb-4">
      <SidebarGroupLabel className="text-xs font-semibold text-foreground/50 uppercase tracking-wider mb-3 px-3">
        {title}
      </SidebarGroupLabel>
      <SidebarGroupContent>
        <SidebarMenu className="space-y-2">
          {items.map((item) => (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton asChild className="w-full">
                <Link to={item.url} className={`${getNavCls(item.url)} px-3 py-2.5 flex items-center group`}>
                  <item.icon className="h-4 w-4 flex-shrink-0 group-hover:scale-110 transition-transform duration-200" />
                  {!collapsed && <span className="font-medium ml-3">{item.title}</span>}
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  );

  return (
    <Sidebar
      className={collapsed ? "w-16" : "w-64"}
      collapsible="icon"
    >
      <SidebarContent className="glass-sidebar">
        {/* Modern Glass Header */}
        <div className="p-6 border-b border-primary/10">
          {!collapsed ? (
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-primary/20 rounded-xl flex items-center justify-center backdrop-blur-sm border border-primary/30">
                <Store className="w-5 h-5 text-primary" />
              </div>
              <div>
                <span className="font-bold text-foreground">AppCraft Pro</span>
                <p className="text-xs text-foreground/60">Business Platform</p>
              </div>
            </div>
          ) : (
            <div className="flex justify-center">
              <div className="w-10 h-10 bg-primary/20 rounded-xl flex items-center justify-center backdrop-blur-sm border border-primary/30">
                <Store className="w-5 h-5 text-primary" />
              </div>
            </div>
          )}
        </div>

        {/* Navigation Groups */}
        <div className="flex-1 overflow-y-auto py-6 space-y-1">
          {renderNavGroup("Build", buildItems)}
          {renderNavGroup("Business", sellItems)}
          {renderNavGroup("Develop", developItems)}
          {renderNavGroup("Support", supportItems)}
        </div>
      </SidebarContent>
    </Sidebar>
  );
}