@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern Minimal Design System - Enhanced for Dashboard */

@layer base {
  :root {
    /* Modern Clean Background */
    --background: 0 0% 100%;
    --foreground: 222 84% 4.9%;

    /* Clean Card System */
    --card: 0 0% 100%;
    --card-foreground: 222 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 84% 4.9%;

    /* Modern Primary - Refined Blue */
    --primary: 221 83% 53%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 221 83% 48%;
    --primary-light: 221 83% 97%;
    --primary-subtle: 221 83% 99%;

    /* Clean Neutrals */
    --secondary: 210 40% 98%;
    --secondary-foreground: 222 84% 4.9%;

    --muted: 210 40% 96%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 210 40% 98%;
    --accent-foreground: 222 84% 4.9%;

    /* Status Colors - Modern & Clean */
    --success: 142 71% 45%;
    --success-foreground: 0 0% 100%;
    --success-light: 142 71% 97%;

    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;
    --warning-light: 38 92% 97%;

    --destructive: 0 72% 51%;
    --destructive-foreground: 0 0% 100%;
    --destructive-light: 0 72% 97%;

    --info: 199 89% 48%;
    --info-foreground: 0 0% 100%;
    --info-light: 199 89% 97%;

    /* Clean Borders & Radii */
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 221 83% 53%;

    --radius: 0.75rem;
  }

  .dark {
    --background: 222 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222 84% 4.9%;
    --secondary: 217 32% 17%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217 32% 17%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217 32% 17%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217 32% 17%;
    --input: 217 32% 17%;
    --ring: 212 72% 59%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Dashboard-specific styles for Metalic design */
@layer components {
  /* Modern card shadows matching Metalic design */
  .dashboard-card {
    @apply bg-white border-0 rounded-xl transition-all duration-200;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }
  
  .dashboard-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  /* Smooth chart animations */
  .chart-bar {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .chart-bar:hover {
    filter: brightness(1.1);
    transform: scaleY(1.02);
  }

  /* Clean metric cards */
  .metric-card {
    @apply dashboard-card;
  }

  /* Beautiful button styles */
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white transition-all duration-200;
    box-shadow: 0 1px 3px rgba(59, 130, 246, 0.3);
  }
  
  .btn-primary:hover {
    box-shadow: 0 2px 6px rgba(59, 130, 246, 0.4);
    transform: translateY(-1px);
  }

  /* Clean form elements */
  .form-select {
    @apply border border-gray-200 rounded-lg px-3 py-1.5 bg-white text-sm;
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20;
    @apply transition-all duration-200;
  }

  /* Smooth chart paths */
  .chart-path {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
    animation: drawLine 2s ease-in-out forwards;
  }

  @keyframes drawLine {
    to {
      stroke-dashoffset: 0;
    }
  }

  /* Beautiful gradient backgrounds */
  .gradient-blue {
    background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
  }

  /* Clean text hierarchy */
  .text-heading {
    @apply text-2xl font-semibold text-gray-900 tracking-tight;
  }
  
  .text-metric {
    @apply text-2xl font-semibold text-gray-900;
  }
  
  .text-label {
    @apply text-sm font-medium text-gray-500;
  }
  
  .text-subtitle {
    @apply text-xs text-gray-400;
  }

  /* Hover states for interactive elements */
  .hover-lift {
    @apply transition-all duration-200;
  }
  
  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }

  /* Clean loading states */
  .skeleton {
    @apply animate-pulse bg-gray-200 rounded;
  }

  /* Status indicators */
  .status-positive {
    @apply text-emerald-600 bg-emerald-50 border-emerald-200;
  }
  
  .status-negative {
    @apply text-red-600 bg-red-50 border-red-200;
  }
  
  .status-neutral {
    @apply text-gray-600 bg-gray-50 border-gray-200;
  }

  /* Chart tooltips */
  .chart-tooltip {
    @apply bg-gray-900 text-white text-xs rounded-lg px-3 py-2;
    @apply opacity-0 transition-opacity duration-200;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  .chart-tooltip.show {
    @apply opacity-100;
  }

  /* Responsive dashboard grid */
  .dashboard-grid {
    @apply grid gap-6;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }

  /* Clean scrollbars */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }
}

/* Print styles for reports */
@media print {
  .no-print {
    display: none !important;
  }
  
  .dashboard-card {
    box-shadow: none !important;
    border: 1px solid #e2e8f0 !important;
    break-inside: avoid;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .dashboard-card {
    border: 2px solid #000 !important;
    box-shadow: none !important;
  }
  
  .text-gray-500 {
    color: #000 !important;
  }
  
  .text-gray-400 {
    color: #333 !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .chart-path {
    animation: none !important;
  }
  
  .transition-all {
    transition: none !important;
  }
  
  .hover-lift:hover {
    transform: none !important;
  }
}

/* Focus styles for accessibility */
.focus-visible {
  @apply outline-none ring-2 ring-blue-500 ring-opacity-50;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .text-heading {
    @apply text-xl;
  }
  
  .metric-card {
    @apply p-4;
  }
}