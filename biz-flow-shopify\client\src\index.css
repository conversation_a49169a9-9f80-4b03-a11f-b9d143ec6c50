@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern Light Theme Design System */

@layer base {
  :root {
    /* Light modern background */
    --background: 220 20% 98%;
    --foreground: 220 40% 8%;

    /* Light glass surfaces with subtle shadows */
    --card: 220 20% 96%;
    --card-foreground: 220 40% 8%;

    --popover: 220 20% 96%;
    --popover-foreground: 220 40% 8%;

    /* Vibrant modern primary */
    --primary: 220 95% 55%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 220 95% 50%;
    --primary-light: 220 95% 95%;
    --primary-subtle: 220 95% 98%;

    /* Light modern neutrals */
    --secondary: 220 20% 92%;
    --secondary-foreground: 220 40% 12%;

    --muted: 220 20% 92%;
    --muted-foreground: 220 30% 45%;

    --accent: 200 85% 55%;
    --accent-foreground: 0 0% 100%;

    /* Modern status colors */
    --success: 140 70% 45%;
    --success-foreground: 0 0% 100%;
    --success-light: 140 70% 95%;

    --warning: 45 85% 55%;
    --warning-foreground: 0 0% 100%;
    --warning-light: 45 85% 95%;

    --destructive: 0 75% 60%;
    --destructive-foreground: 0 0% 100%;
    --destructive-light: 0 75% 95%;

    --info: 200 85% 55%;
    --info-foreground: 0 0% 100%;
    --info-light: 200 85% 95%;

    /* Light glass borders and inputs */
    --border: 220 20% 85%;
    --input: 220 20% 94%;
    --ring: 220 95% 55%;

    /* Light glass sidebar */
    --sidebar-background: 220 20% 97%;
    --sidebar-foreground: 220 40% 8%;
    --sidebar-primary: 220 95% 55%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 220 20% 94%;
    --sidebar-accent-foreground: 220 40% 8%;
    --sidebar-border: 220 20% 88%;
    --sidebar-ring: 220 95% 55%;

    /* Modern glass spacing */
    --radius: 1rem;
    --radius-lg: 1.25rem;
    --radius-xl: 1.5rem;

    /* Light modern shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-base: 0 1px 3px 0 rgb(0 0 0 / 0.08), 0 1px 2px -1px rgb(0 0 0 / 0.08);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.12), 0 4px 6px -4px rgb(0 0 0 / 0.12);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.15), 0 8px 10px -6px rgb(0 0 0 / 0.15);
    --shadow-glow: 0 0 20px rgb(59 130 246 / 0.15);

    /* Smooth transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-base: 200ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);

    /* Light modern gradients */
    --gradient-primary: linear-gradient(135deg, hsl(220 95% 55%) 0%, hsl(200 85% 55%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(220 20% 92%) 0%, hsl(220 20% 96%) 100%);
    --gradient-accent: linear-gradient(135deg, hsl(220 95% 55%) 0%, hsl(200 85% 55%) 50%, hsl(240 85% 60%) 100%);
    --gradient-bg: linear-gradient(135deg, hsl(220 20% 98%) 0%, hsl(220 20% 96%) 100%);

    /* Modern accent colors */
    --electric-blue: 220 95% 55%;
    --cyan-blue: 200 85% 55%;
    --indigo-blue: 240 85% 60%;
    --sky-blue: 200 100% 70%;
    --violet-blue: 260 85% 65%;
    --terminal-bg: 220 20% 94%;
    --code-bg: 220 20% 96%;
    
    --chart-1: 220 95% 55%;
    --chart-2: 200 85% 55%;
    --chart-3: 240 85% 60%;
    --chart-4: 160 70% 50%;
    --chart-5: 260 85% 65%;
  }

  .dark {
    --background: 220 30% 4%;
    --foreground: 220 20% 95%;
    --card: 220 30% 6%;
    --card-foreground: 220 20% 95%;
    --popover: 220 30% 6%;
    --popover-foreground: 220 20% 95%;
    --primary: 220 95% 60%;
    --primary-foreground: 0 0% 100%;
    --secondary: 220 30% 8%;
    --secondary-foreground: 220 20% 90%;
    --muted: 220 30% 8%;
    --muted-foreground: 220 20% 70%;
    --accent: 200 85% 60%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 75% 65%;
    --destructive-foreground: 0 0% 100%;
    --border: 220 50% 12%;
    --input: 220 50% 12%;
    --ring: 210 100% 60%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    font-optical-sizing: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Make the app always use dark theme */
  html {
    @apply dark;
  }
}

/* Modern layout components */
@layer components {
  .dashboard-section {
    @apply space-y-6;
  }

  .stats-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4;
  }

  .content-grid {
    @apply grid grid-cols-1 lg:grid-cols-3 gap-6;
  }

  .page-layout {
    @apply min-h-screen bg-background;
  }

  .page-content {
    @apply container mx-auto px-4 py-6 max-w-7xl;
  }

  .metric-card {
    @apply border-border/20 hover:border-primary/30 transition-all duration-500;
    @apply shadow-lg hover:shadow-xl;
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(59, 130, 246, 0.15);
    border-radius: var(--radius-lg);
  }

  .metric-card:hover {
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 20px 40px -10px rgba(59, 130, 246, 0.15);
    border-color: rgba(59, 130, 246, 0.25);
    transform: translateY(-2px);
  }

  .glass-card {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(59, 130, 246, 0.15);
    border-radius: var(--radius-lg);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  }

  .glass-button {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: var(--radius);
    transition: all 0.3s ease;
  }

  .glass-button:hover {
    background: rgba(59, 130, 246, 0.1);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.15);
    transform: translateY(-1px);
    border-color: rgba(59, 130, 246, 0.3);
  }

  .glass-input {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(59, 130, 246, 0.15);
    border-radius: var(--radius);
  }

  .glass-input:focus {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(59, 130, 246, 0.4);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .glass-sidebar {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(20px);
    border-right: 1px solid rgba(59, 130, 246, 0.15);
  }

  .account-switcher-dropdown {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: var(--radius-lg);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }

  .code-bg {
    @apply bg-[hsl(var(--code-bg))];
  }

  .gradient-neon {
    background: var(--gradient-neon);
  }

  .gradient-primary {
    background: var(--gradient-primary);
  }

  .gradient-terminal {
    background: var(--gradient-terminal);
  }

  /* Status badges with neon colors */
  .status-active {
    @apply bg-green-500/20 text-green-400 border-green-500/30;
  }

  .status-pending {
    @apply bg-yellow-500/20 text-yellow-400 border-yellow-500/30;
  }

  .status-inactive {
    @apply bg-red-500/20 text-red-400 border-red-500/30;
  }

  .status-processing {
    @apply bg-blue-500/20 text-blue-400 border-blue-500/30;
  }

  .status-completed {
    @apply bg-emerald-500/20 text-emerald-400 border-emerald-500/30;
  }

  .status-draft {
    @apply bg-slate-500/20 text-slate-400 border-slate-500/30;
  }

  /* Glow effects for interactive elements */
  .btn-primary {
    @apply bg-primary hover:bg-primary/90 text-primary-foreground;
    box-shadow: 0 0 20px hsl(var(--primary) / 0.3);
  }

  .btn-primary:hover {
    box-shadow: 0 0 30px hsl(var(--primary) / 0.5);
  }

  .input-glow:focus {
    @apply ring-2 ring-primary/50 border-primary/50;
    box-shadow: 0 0 10px hsl(var(--primary) / 0.2);
  }

  /* Custom scrollbar for terminal feel */
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-primary/60 rounded-full;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-primary/80;
  }

  /* Animations */
  @keyframes pulse-neon {
    0%, 100% {
      opacity: 1;
      text-shadow: 0 0 10px hsl(var(--primary) / 0.5);
    }
    50% {
      opacity: 0.8;
      text-shadow: 0 0 20px hsl(var(--primary) / 0.8);
    }
  }

  .animate-pulse-neon {
    animation: pulse-neon 2s ease-in-out infinite;
  }

  @keyframes glow {
    0%, 100% {
      box-shadow: 0 0 20px hsl(var(--primary) / 0.3);
    }
    50% {
      box-shadow: 0 0 30px hsl(var(--primary) / 0.5);
    }
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite;
  }
}

/* Terminal-inspired typography */
@layer utilities {
  .font-mono {
    font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  }

  .text-neon-green {
    @apply text-[hsl(var(--neon-green))];
  }

  .text-neon-purple {
    @apply text-[hsl(var(--neon-purple))];
  }

  .text-neon-blue {
    @apply text-[hsl(var(--neon-blue))];
  }

  .text-neon-orange {
    @apply text-[hsl(var(--neon-orange))];
  }

  .text-neon-pink {
    @apply text-[hsl(var(--neon-pink))];
  }

  .bg-neon-green {
    @apply bg-[hsl(var(--neon-green))];
  }

  .bg-neon-purple {
    @apply bg-[hsl(var(--neon-purple))];
  }

  .bg-neon-blue {
    @apply bg-[hsl(var(--neon-blue))];
  }

  .bg-neon-orange {
    @apply bg-[hsl(var(--neon-orange))];
  }

  .bg-neon-pink {
    @apply bg-[hsl(var(--neon-pink))];
  }

  .border-neon-green {
    @apply border-[hsl(var(--neon-green))];
  }

  .border-neon-purple {
    @apply border-[hsl(var(--neon-purple))];
  }

  .border-neon-blue {
    @apply border-[hsl(var(--neon-blue))];
  }

  .border-neon-orange {
    @apply border-[hsl(var(--neon-orange))];
  }

  .border-neon-pink {
    @apply border-[hsl(var(--neon-pink))];
  }
}