import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  FileText,
  MessageSquare,
  User,
  Tag,
  Clock,
  CheckCircle,
  Truck,
  Package,
  CreditCard,
  Printer,
  FileDown,
} from "lucide-react";

const TimelineItem = ({ icon, children, isLast }: any) => (
  <div className="flex gap-4">
    <div className="flex flex-col items-center">
      <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
        {icon}
      </div>
      {!isLast && <div className="w-px h-full bg-gray-200" />}
    </div>
    <div className="pb-8">{children}</div>
  </div>
);

export const OrderDetailsModal = ({ order, open, onOpenChange }: any) => {
  if (!order) return null;

  const getTimelineIcon = (type: string) => {
    switch (type) {
      case "placed": return <Package className="h-4 w-4 text-gray-500" />;
      case "paid": return <CreditCard className="h-4 w-4 text-gray-500" />;
      case "fulfilled": return <Truck className="h-4 w-4 text-gray-500" />;
      case "delivered": return <CheckCircle className="h-4 w-4 text-gray-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl p-0">
        <div className="grid grid-cols-1 md:grid-cols-3">
          {/* Main Content */}
          <div className="md:col-span-2 p-6">
            <DialogHeader className="flex flex-row justify-between items-start mb-4">
              <div>
                <DialogTitle className="text-2xl font-bold flex items-center gap-2">
                  #{order.id}
                  <Badge variant="outline" className="text-sm font-medium">{order.fulfillmentStatus}</Badge>
                  <Badge variant="secondary" className="text-sm font-medium">{order.paymentStatus}</Badge>
                </DialogTitle>
                <DialogDescription className="mt-1">
                  {new Date(order.date).toLocaleString()} • {order.items.length} items
                </DialogDescription>
              </div>
              <div className="flex gap-2">
                  <Button variant="outline" size="sm"><Printer className="h-4 w-4 mr-2" /> Print</Button>
                  <Button variant="outline" size="sm"><FileDown className="h-4 w-4 mr-2" /> Export</Button>
              </div>
            </DialogHeader>

            <div className="space-y-6">
              {/* Order Items */}
              <div>
                <h3 className="text-lg font-semibold mb-2">Items</h3>
                <div className="border rounded-lg">
                  {order.items.map((item: any) => (
                    <div key={item.id} className="flex items-center justify-between p-3 border-b last:border-b-0">
                      <div className="flex items-center gap-3">
                        <Avatar className="rounded-md">
                          <AvatarImage src={`https://picsum.photos/seed/${item.id}/40`} />
                          <AvatarFallback>{item.name.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-semibold">{item.name}</p>
                          <p className="text-sm text-gray-500">SKU: {item.sku}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">${item.price.toFixed(2)} x {item.quantity}</p>
                        <p className="font-bold text-lg">${(item.price * item.quantity).toFixed(2)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Payment */}
              <div>
                <h3 className="text-lg font-semibold mb-2">Payment</h3>
                <div className="border rounded-lg p-4 bg-gray-50">
                    <div className="flex justify-between py-1">
                      <span className="text-gray-600">Subtotal</span>
                      <span className="font-medium">${order.payment.subtotal.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between py-1">
                      <span className="text-gray-600">Shipping</span>
                      <span className="font-medium">${order.payment.shipping.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between py-1">
                      <span className="text-gray-600">Taxes</span>
                      <span className="font-medium">${order.payment.tax.toFixed(2)}</span>
                    </div>
                    <Separator className="my-2" />
                    <div className="flex justify-between py-1 font-bold text-lg">
                      <span>Total</span>
                      <span>${order.total.toFixed(2)}</span>
                    </div>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="bg-gray-50 border-l p-6 space-y-6">
            {/* Customer */}
            <div>
              <h3 className="text-lg font-semibold mb-2 flex items-center"><User className="h-4 w-4 mr-2" /> Customer</h3>
              <div className="text-sm">
                <p className="font-semibold text-blue-600">{order.customer.name}</p>
                <p className="text-gray-500">{order.customer.email}</p>
                <p className="mt-2 font-medium">Shipping Address</p>
                <p className="text-gray-500">{order.customer.shippingAddress}</p>
              </div>
            </div>
            
            <Separator />

            {/* Notes */}
            <div>
              <h3 className="text-lg font-semibold mb-2 flex items-center"><MessageSquare className="h-4 w-4 mr-2" /> Notes</h3>
              <p className="text-sm text-gray-500 italic">{order.notes || "No notes for this order."}</p>
            </div>

            <Separator />

            {/* Tags */}
            <div>
                <h3 className="text-lg font-semibold mb-2 flex items-center"><Tag className="h-4 w-4 mr-2" /> Tags</h3>
                <div className="flex flex-wrap gap-2">
                    {order.tags.map((tag: string) => <Badge key={tag} variant="secondary">{tag}</Badge>)}
                </div>
            </div>
            
            <Separator />
            
            {/* Timeline */}
            <div>
              <h3 className="text-lg font-semibold mb-2">Timeline</h3>
              <div>
                {order.timeline.map((event: any, index: number) => (
                  <TimelineItem 
                    key={index} 
                    icon={getTimelineIcon(event.type)}
                    isLast={index === order.timeline.length - 1}
                  >
                    <p className="font-medium">{event.title}</p>
                    <p className="text-sm text-gray-500">{new Date(event.date).toLocaleString()}</p>
                  </TimelineItem>
                ))}
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}; 