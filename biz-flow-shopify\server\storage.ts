import { 
  users, products, customers, orders, orderItems, supportTickets, analytics,
  type User, type InsertUser, type UpdateUser,
  type Product, type InsertProduct, type UpdateProduct,
  type Customer, type InsertCustomer, type UpdateCustomer,
  type Order, type InsertOrder, type UpdateOrder,
  type OrderItem, type InsertOrderItem,
  type SupportTicket, type InsertSupportTicket, type UpdateSupportTicket,
  ORDER_STATUS, PAYMENT_STATUS, CUSTOMER_TIERS, SUPPORT_STATUS, PRODUCT_STATUS
} from "@shared/schema";

// Enhanced storage interface with comprehensive CRUD operations
export interface IStorage {
  // User operations
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, updates: UpdateUser): Promise<User | undefined>;
  deleteUser(id: number): Promise<boolean>;
  
  // Product operations
  getProduct(id: number): Promise<Product | undefined>;
  getProducts(filters?: { category?: string; status?: string }): Promise<Product[]>;
  createProduct(product: InsertProduct): Promise<Product>;
  updateProduct(id: number, updates: UpdateProduct): Promise<Product | undefined>;
  deleteProduct(id: number): Promise<boolean>;
  
  // Customer operations
  getCustomer(id: number): Promise<Customer | undefined>;
  getCustomerByEmail(email: string): Promise<Customer | undefined>;
  getCustomers(filters?: { tier?: string; status?: string }): Promise<Customer[]>;
  createCustomer(customer: InsertCustomer): Promise<Customer>;
  updateCustomer(id: number, updates: UpdateCustomer): Promise<Customer | undefined>;
  deleteCustomer(id: number): Promise<boolean>;
  
  // Order operations
  getOrder(id: number): Promise<Order | undefined>;
  getOrderByNumber(orderNumber: string): Promise<Order | undefined>;
  getOrders(filters?: { customerId?: number; status?: string }): Promise<Order[]>;
  createOrder(order: InsertOrder): Promise<Order>;
  updateOrder(id: number, updates: UpdateOrder): Promise<Order | undefined>;
  deleteOrder(id: number): Promise<boolean>;
  
  // Order item operations
  getOrderItems(orderId: number): Promise<OrderItem[]>;
  addOrderItem(item: InsertOrderItem): Promise<OrderItem>;
  updateOrderItem(id: number, updates: Partial<OrderItem>): Promise<OrderItem | undefined>;
  deleteOrderItem(id: number): Promise<boolean>;
  
  // Support ticket operations
  getSupportTicket(id: number): Promise<SupportTicket | undefined>;
  getSupportTickets(filters?: { customerId?: number; status?: string }): Promise<SupportTicket[]>;
  createSupportTicket(ticket: InsertSupportTicket): Promise<SupportTicket>;
  updateSupportTicket(id: number, updates: UpdateSupportTicket): Promise<SupportTicket | undefined>;
  deleteSupportTicket(id: number): Promise<boolean>;
  
  // Analytics operations
  getAnalytics(metric: string, period: string): Promise<any[]>;
  recordAnalytic(metric: string, value: number, period: string, metadata?: any): Promise<void>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private products: Map<number, Product>;
  private customers: Map<number, Customer>;
  private orders: Map<number, Order>;
  private orderItems: Map<number, OrderItem>;
  private supportTickets: Map<number, SupportTicket>;
  private analytics: Map<string, any>;
  
  private currentUserId: number;
  private currentProductId: number;
  private currentCustomerId: number;
  private currentOrderId: number;
  private currentOrderItemId: number;
  private currentTicketId: number;

  constructor() {
    this.users = new Map();
    this.products = new Map();
    this.customers = new Map();
    this.orders = new Map();
    this.orderItems = new Map();
    this.supportTickets = new Map();
    this.analytics = new Map();
    
    this.currentUserId = 1;
    this.currentProductId = 1;
    this.currentCustomerId = 1;
    this.currentOrderId = 1;
    this.currentOrderItemId = 1;
    this.currentTicketId = 1;
    
    // Initialize with sample data
    this.initializeSampleData();
  }

  private initializeSampleData() {
    // Sample users
    const sampleUsers: User[] = [
      {
        id: 1,
        username: "admin",
        email: "<EMAIL>",
        password: "admin123",
        firstName: "Admin",
        lastName: "User",
        role: "admin",
        isActive: true,
        avatar: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];
    
    // Sample customers
    const sampleCustomers: Customer[] = [
      {
        id: 1,
        firstName: "Alice",
        lastName: "Johnson",
        email: "<EMAIL>",
        phone: "+****************",
        company: "Tech Solutions Inc.",
        address: { street: "123 Main St", city: "San Francisco", state: "CA", zip: "94105" },
        tier: CUSTOMER_TIERS.VIP,
        totalSpent: "2847.50",
        totalOrders: 24,
        lastOrderDate: new Date(),
        status: "active",
        notes: "VIP customer with excellent purchase history",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 2,
        firstName: "Bob",
        lastName: "Smith",
        email: "<EMAIL>",
        phone: "+****************",
        company: "Design Studio",
        address: { street: "456 Oak Ave", city: "New York", state: "NY", zip: "10001" },
        tier: CUSTOMER_TIERS.PREMIUM,
        totalSpent: "1456.80",
        totalOrders: 15,
        lastOrderDate: new Date(),
        status: "active",
        notes: "Regular customer, prefers e-commerce solutions",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];
    
    // Sample products
    const sampleProducts: Product[] = [
      {
        id: 1,
        name: "Premium Dashboard Template",
        description: "Modern, responsive dashboard template with advanced features",
        category: "Templates",
        price: "89.00",
        cost: "25.00",
        stock: 45,
        sku: "DASH-001",
        status: PRODUCT_STATUS.ACTIVE,
        images: ["/images/dashboard-template.jpg"],
        tags: ["dashboard", "template", "premium"],
        metadata: { featured: true, downloads: 234 },
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 2,
        name: "E-commerce Starter Kit",
        description: "Complete e-commerce solution with payment integration",
        category: "Kits",
        price: "156.00",
        cost: "40.00",
        stock: 23,
        sku: "ECOM-001",
        status: PRODUCT_STATUS.ACTIVE,
        images: ["/images/ecommerce-kit.jpg"],
        tags: ["ecommerce", "starter", "kit"],
        metadata: { featured: true, downloads: 156 },
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];
    
    // Sample orders
    const sampleOrders: Order[] = [
      {
        id: 1,
        orderNumber: "ORD-2024-001",
        customerId: 1,
        status: ORDER_STATUS.DELIVERED,
        paymentStatus: PAYMENT_STATUS.PAID,
        paymentMethod: "Credit Card",
        subtotal: "118.00",
        tax: "0.00",
        shipping: "0.00",
        total: "118.00",
        currency: "USD",
        shippingAddress: { street: "123 Main St", city: "San Francisco", state: "CA", zip: "94105" },
        billingAddress: { street: "123 Main St", city: "San Francisco", state: "CA", zip: "94105" },
        notes: "Digital download order",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];
    
    // Sample support tickets
    const sampleTickets: SupportTicket[] = [
      {
        id: 1,
        ticketNumber: "TK-2024-001",
        customerId: 1,
        subject: "Payment processing issue",
        description: "Customer experiencing issues with payment processing",
        priority: "high",
        status: SUPPORT_STATUS.OPEN,
        category: "billing",
        assignedTo: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];
    
    // Initialize data
    sampleUsers.forEach(user => this.users.set(user.id, user));
    sampleCustomers.forEach(customer => this.customers.set(customer.id, customer));
    sampleProducts.forEach(product => this.products.set(product.id, product));
    sampleOrders.forEach(order => this.orders.set(order.id, order));
    sampleTickets.forEach(ticket => this.supportTickets.set(ticket.id, ticket));
    
    // Set current IDs
    this.currentUserId = Math.max(...Array.from(this.users.keys())) + 1;
    this.currentCustomerId = Math.max(...Array.from(this.customers.keys())) + 1;
    this.currentProductId = Math.max(...Array.from(this.products.keys())) + 1;
    this.currentOrderId = Math.max(...Array.from(this.orders.keys())) + 1;
    this.currentTicketId = Math.max(...Array.from(this.supportTickets.keys())) + 1;
  }

  // User operations
  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(user => user.username === username);
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(user => user.email === email);
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentUserId++;
    const user: User = { 
      ...insertUser, 
      id,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.users.set(id, user);
    return user;
  }

  async updateUser(id: number, updates: UpdateUser): Promise<User | undefined> {
    const user = this.users.get(id);
    if (!user) return undefined;
    
    const updatedUser = { ...user, ...updates, updatedAt: new Date() };
    this.users.set(id, updatedUser);
    return updatedUser;
  }

  async deleteUser(id: number): Promise<boolean> {
    return this.users.delete(id);
  }

  // Product operations
  async getProduct(id: number): Promise<Product | undefined> {
    return this.products.get(id);
  }

  async getProducts(filters?: { category?: string; status?: string }): Promise<Product[]> {
    let products = Array.from(this.products.values());
    
    if (filters?.category) {
      products = products.filter(p => p.category === filters.category);
    }
    if (filters?.status) {
      products = products.filter(p => p.status === filters.status);
    }
    
    return products;
  }

  async createProduct(insertProduct: InsertProduct): Promise<Product> {
    const id = this.currentProductId++;
    const product: Product = { 
      ...insertProduct, 
      id,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.products.set(id, product);
    return product;
  }

  async updateProduct(id: number, updates: UpdateProduct): Promise<Product | undefined> {
    const product = this.products.get(id);
    if (!product) return undefined;
    
    const updatedProduct = { ...product, ...updates, updatedAt: new Date() };
    this.products.set(id, updatedProduct);
    return updatedProduct;
  }

  async deleteProduct(id: number): Promise<boolean> {
    return this.products.delete(id);
  }

  // Customer operations
  async getCustomer(id: number): Promise<Customer | undefined> {
    return this.customers.get(id);
  }

  async getCustomerByEmail(email: string): Promise<Customer | undefined> {
    return Array.from(this.customers.values()).find(customer => customer.email === email);
  }

  async getCustomers(filters?: { tier?: string; status?: string }): Promise<Customer[]> {
    let customers = Array.from(this.customers.values());
    
    if (filters?.tier) {
      customers = customers.filter(c => c.tier === filters.tier);
    }
    if (filters?.status) {
      customers = customers.filter(c => c.status === filters.status);
    }
    
    return customers;
  }

  async createCustomer(insertCustomer: InsertCustomer): Promise<Customer> {
    const id = this.currentCustomerId++;
    const customer: Customer = { 
      ...insertCustomer, 
      id,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.customers.set(id, customer);
    return customer;
  }

  async updateCustomer(id: number, updates: UpdateCustomer): Promise<Customer | undefined> {
    const customer = this.customers.get(id);
    if (!customer) return undefined;
    
    const updatedCustomer = { ...customer, ...updates, updatedAt: new Date() };
    this.customers.set(id, updatedCustomer);
    return updatedCustomer;
  }

  async deleteCustomer(id: number): Promise<boolean> {
    return this.customers.delete(id);
  }

  // Order operations
  async getOrder(id: number): Promise<Order | undefined> {
    return this.orders.get(id);
  }

  async getOrderByNumber(orderNumber: string): Promise<Order | undefined> {
    return Array.from(this.orders.values()).find(order => order.orderNumber === orderNumber);
  }

  async getOrders(filters?: { customerId?: number; status?: string }): Promise<Order[]> {
    let orders = Array.from(this.orders.values());
    
    if (filters?.customerId) {
      orders = orders.filter(o => o.customerId === filters.customerId);
    }
    if (filters?.status) {
      orders = orders.filter(o => o.status === filters.status);
    }
    
    return orders;
  }

  async createOrder(insertOrder: InsertOrder): Promise<Order> {
    const id = this.currentOrderId++;
    const order: Order = { 
      ...insertOrder, 
      id,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.orders.set(id, order);
    return order;
  }

  async updateOrder(id: number, updates: UpdateOrder): Promise<Order | undefined> {
    const order = this.orders.get(id);
    if (!order) return undefined;
    
    const updatedOrder = { ...order, ...updates, updatedAt: new Date() };
    this.orders.set(id, updatedOrder);
    return updatedOrder;
  }

  async deleteOrder(id: number): Promise<boolean> {
    return this.orders.delete(id);
  }

  // Order item operations
  async getOrderItems(orderId: number): Promise<OrderItem[]> {
    return Array.from(this.orderItems.values()).filter(item => item.orderId === orderId);
  }

  async addOrderItem(insertItem: InsertOrderItem): Promise<OrderItem> {
    const id = this.currentOrderItemId++;
    const item: OrderItem = { ...insertItem, id };
    this.orderItems.set(id, item);
    return item;
  }

  async updateOrderItem(id: number, updates: Partial<OrderItem>): Promise<OrderItem | undefined> {
    const item = this.orderItems.get(id);
    if (!item) return undefined;
    
    const updatedItem = { ...item, ...updates };
    this.orderItems.set(id, updatedItem);
    return updatedItem;
  }

  async deleteOrderItem(id: number): Promise<boolean> {
    return this.orderItems.delete(id);
  }

  // Support ticket operations
  async getSupportTicket(id: number): Promise<SupportTicket | undefined> {
    return this.supportTickets.get(id);
  }

  async getSupportTickets(filters?: { customerId?: number; status?: string }): Promise<SupportTicket[]> {
    let tickets = Array.from(this.supportTickets.values());
    
    if (filters?.customerId) {
      tickets = tickets.filter(t => t.customerId === filters.customerId);
    }
    if (filters?.status) {
      tickets = tickets.filter(t => t.status === filters.status);
    }
    
    return tickets;
  }

  async createSupportTicket(insertTicket: InsertSupportTicket): Promise<SupportTicket> {
    const id = this.currentTicketId++;
    const ticket: SupportTicket = { 
      ...insertTicket, 
      id,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.supportTickets.set(id, ticket);
    return ticket;
  }

  async updateSupportTicket(id: number, updates: UpdateSupportTicket): Promise<SupportTicket | undefined> {
    const ticket = this.supportTickets.get(id);
    if (!ticket) return undefined;
    
    const updatedTicket = { ...ticket, ...updates, updatedAt: new Date() };
    this.supportTickets.set(id, updatedTicket);
    return updatedTicket;
  }

  async deleteSupportTicket(id: number): Promise<boolean> {
    return this.supportTickets.delete(id);
  }

  // Analytics operations
  async getAnalytics(metric: string, period: string): Promise<any[]> {
    const key = `${metric}-${period}`;
    return this.analytics.get(key) || [];
  }

  async recordAnalytic(metric: string, value: number, period: string, metadata?: any): Promise<void> {
    const key = `${metric}-${period}`;
    const existing = this.analytics.get(key) || [];
    existing.push({ value, date: new Date(), metadata });
    this.analytics.set(key, existing);
  }
}

export const storage = new MemStorage();
