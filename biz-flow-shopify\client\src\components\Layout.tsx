import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/AppSidebar";
import { AccountSwitcher } from "@/components/AccountSwitcher";
import { Toaster } from "@/components/ui/toaster";
import { ReactNode } from "react";

interface LayoutProps {
  children: ReactNode;
}

export function Layout({ children }: LayoutProps) {
  return (
    <SidebarProvider>
      <div className="flex min-h-screen w-full bg-background">
        <AppSidebar />
        <div className="flex-1 flex flex-col">
          {/* Modern Glass Header */}
          <header className="h-16 border-b border-primary/10 bg-card/30 backdrop-blur-xl flex items-center px-6 sticky top-0 z-50">
            <div className="flex items-center gap-4">
              <SidebarTrigger className="glass-button p-2" />
              <div className="h-5 w-px bg-primary/20" />
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-primary/20 rounded-xl flex items-center justify-center backdrop-blur-sm border border-primary/30">
                  <span className="text-sm font-bold text-primary">A</span>
                </div>
                <div>
                  <span className="font-semibold text-foreground">AppCraft Pro</span>
                  <div className="text-xs text-foreground/60">Business Platform</div>
                </div>
              </div>
            </div>
            
            <div className="ml-auto flex items-center gap-4">
              <div className="flex items-center gap-2 text-sm text-foreground/70 bg-primary/10 px-3 py-1.5 rounded-xl backdrop-blur-sm border border-primary/20">
                <div className="w-2 h-2 bg-primary rounded-full animate-pulse shadow-sm shadow-primary/50"></div>
                <span>Live</span>
              </div>
              <AccountSwitcher />
            </div>
          </header>
          
          {/* Main Content Area */}
          <main className="flex-1 overflow-auto bg-background">
            {children}
          </main>
        </div>
      </div>
      <Toaster />
    </SidebarProvider>
  );
}