import { TrendingUp, TrendingDown, DollarSign, Package, Users, ShoppingCart } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const stats = [
  {
    title: "Total Revenue",
    value: "$45,231.89",
    change: "+20.1%",
    trend: "up",
    icon: DollarSign,
    color: "primary"
  },
  {
    title: "Total Orders",
    value: "2,451",
    change: "+15.2%",
    trend: "up",
    icon: ShoppingCart,
    color: "success"
  },
  {
    title: "Products Sold",
    value: "12,234",
    change: "+8.5%",
    trend: "up",
    icon: Package,
    color: "muted"
  },
  {
    title: "Active Customers",
    value: "1,234",
    change: "-2.1%",
    trend: "down",
    icon: Users,
    color: "warning"
  },
];

export function DashboardStats() {
  const getIconBg = (color: string) => {
    switch (color) {
      case "primary": return "bg-primary/10 text-primary";
      case "success": return "bg-success/10 text-success";
      case "warning": return "bg-warning/10 text-warning";
      default: return "bg-muted text-muted-foreground";
    }
  };

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
      {stats.map((stat) => (
        <Card key={stat.title} className="border-0 shadow-card bg-background hover:shadow-elegant transition-shadow duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              {stat.title}
            </CardTitle>
            <div className={`p-2.5 rounded-xl ${getIconBg(stat.color)}`}>
              <stat.icon className="h-4 w-4" />
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl font-bold mb-1">{stat.value}</div>
            <div className="flex items-center text-sm">
              {stat.trend === "up" ? (
                <TrendingUp className="mr-1.5 h-3 w-3 text-success" />
              ) : (
                <TrendingDown className="mr-1.5 h-3 w-3 text-destructive" />
              )}
              <span className={stat.trend === "up" ? "text-success" : "text-destructive"}>
                {stat.change}
              </span>
              <span className="text-muted-foreground ml-1.5">from last month</span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}