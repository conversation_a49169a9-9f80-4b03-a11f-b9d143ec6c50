import { ReactNode } from "react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface Column {
  key: string;
  label: string;
  width?: string;
  render?: (value: any, row: any) => ReactNode;
}

interface DataTableProps {
  title?: string;
  description?: string;
  columns: Column[];
  data?: any[];
  actions?: ReactNode;
  className?: string;
  emptyState?: ReactNode;
}

export function DataTable({ 
  title, 
  description, 
  columns, 
  data = [], 
  actions, 
  className,
  emptyState 
}: DataTableProps) {
  // Ensure data is always an array
  const safeData = Array.isArray(data) ? data : [];
  
  return (
    <div className={cn("glass-card", className)}>
      {(title || description || actions) && (
        <div className="p-6 border-b border-primary/10">
          <div className="flex items-start justify-between">
            <div className="space-y-1">
              {title && (
                <h3 className="text-xl font-semibold text-foreground">{title}</h3>
              )}
              {description && (
                <p className="text-sm text-foreground/60">{description}</p>
              )}
            </div>
            {actions && <div className="flex items-center gap-2">{actions}</div>}
          </div>
        </div>
      )}
      
      <div className="px-0">
        {safeData.length === 0 ? (
          <div className="px-6 py-12 text-center">
            {emptyState || (
              <div className="space-y-3">
                <div className="text-foreground/60 text-sm">No data available</div>
                <p className="text-xs text-foreground/40">
                  Data will appear here once it's available
                </p>
              </div>
            )}
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow className="bg-primary/5 border-primary/10">
                {columns.map((column) => (
                  <TableHead 
                    key={column.key}
                    className="font-medium text-foreground/80 h-12"
                    style={{ width: column.width }}
                  >
                    {column.label}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {safeData.map((row, index) => (
                <TableRow 
                  key={index}
                  className="border-primary/10 hover:bg-primary/5 transition-all duration-300"
                >
                  {columns.map((column) => (
                    <TableCell key={column.key} className="py-4 text-foreground/80">
                      {column.render 
                        ? column.render(row[column.key], row)
                        : row[column.key]
                      }
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </div>
    </div>
  );
}