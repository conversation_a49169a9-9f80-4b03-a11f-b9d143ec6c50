import { Layout } from "@/components/Layout";
import { PageContainer } from "@/components/layout/PageContainer";
import { PageHeader } from "@/components/layout/PageHeader";
import { DataTable } from "@/components/layout/DataTable";
import { StatusBadge } from "@/components/layout/StatusBadge";
import { MetricCard } from "@/components/dashboard/MetricCard";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { 
  Filter, 
  Download, 
  Eye, 
  MoreHorizontal,
  TrendingUp,
  Clock,
  Package,
  Star,
  RefreshCw,
  Plus
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";

interface Order {
  id: number;
  orderNumber: string;
  customerId: number;
  total: number;
  status: string;
  paymentStatus: string;
  createdAt: string;
  updatedAt: string;
}

export default function Orders() {
  const { data: orders = [], isLoading } = useQuery<Order[]>({
    queryKey: ['/api/orders'],
  });

  const columns = [
    {
      key: "orderNumber",
      label: "Order ID",
      width: "120px",
      render: (value: string) => (
        <div className="font-medium text-primary">{value}</div>
      )
    },
    {
      key: "customerId",
      label: "Customer",
      render: (value: number) => (
        <div className="flex items-center gap-3">
          <Avatar className="w-8 h-8">
            <AvatarFallback className="text-xs font-medium">
              {value.toString().padStart(2, '0')}
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium">Customer #{value}</div>
            <div className="text-sm text-muted-foreground">customer{value}@example.com</div>
          </div>
        </div>
      )
    },
    {
      key: "total",
      label: "Total",
      width: "100px",
      render: (value: number) => (
        <div className="font-medium">${value.toFixed(2)}</div>
      )
    },
    {
      key: "status",
      label: "Status",
      width: "140px",
      render: (value: string) => <StatusBadge status={value} />
    },
    {
      key: "paymentStatus",
      label: "Payment",
      width: "120px",
      render: (value: string) => <StatusBadge status={value} variant="compact" />
    },
    {
      key: "createdAt",
      label: "Order Date",
      width: "120px",
      render: (value: string) => (
        <div className="text-sm">
          {new Date(value).toLocaleDateString()}
        </div>
      )
    },
    {
      key: "actions",
      label: "Actions",
      width: "100px",
      render: () => (
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm">
            <Eye className="w-4 h-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="w-4 h-4" />
          </Button>
        </div>
      )
    }
  ];

  return (
    <Layout>
      <PageContainer>
        <PageHeader 
          title="Orders Management"
          description="Track and manage customer orders, payments, and fulfillment"
          actions={
            <>
              <Button variant="outline" size="sm">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
              <Button variant="outline" size="sm">
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh
              </Button>
              <Button size="sm">
                <Plus className="w-4 h-4 mr-2" />
                New Order
              </Button>
            </>
          }
        />
        
        <div className="dashboard-section">
          {/* Order Stats */}
          <div className="stats-grid">
            <MetricCard
              title="Total Orders"
              value={orders ? orders.length : 0}
              change="+12%"
              changeType="positive"
              icon={<Package className="w-5 h-5" />}
            />
            
            <MetricCard
              title="Pending Orders"
              value={orders && orders.filter ? orders.filter(order => order.status === 'pending').length : 0}
              description="Needs attention"
              icon={<Clock className="w-5 h-5" />}
            />
            
            <MetricCard
              title="Avg. Order Value"
              value={`$${orders && orders.length > 0 ? (orders.reduce((sum, order) => sum + order.total, 0) / orders.length).toFixed(2) : '0.00'}`}
              change="+8.2%"
              changeType="positive"
              icon={<TrendingUp className="w-5 h-5" />}
            />
            
            <MetricCard
              title="Customer Satisfaction"
              value="4.8/5"
              description="Based on reviews"
              icon={<Star className="w-5 h-5" />}
            />
          </div>
          
          {/* Orders Table */}
          <DataTable
            title="Recent Orders"
            description="View and manage all customer orders"
            columns={columns}
            data={orders}
            emptyState={
              <div className="text-center py-8">
                <Package className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No orders found</h3>
                <p className="text-muted-foreground text-sm">
                  Orders will appear here once customers start placing them
                </p>
              </div>
            }
          />
        </div>
      </PageContainer>
    </Layout>
  );
}