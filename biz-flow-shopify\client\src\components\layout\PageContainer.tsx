import { ReactNode } from "react";
import { cn } from "@/lib/utils";

interface PageContainerProps {
  children: ReactNode;
  className?: string;
}

export function PageContainer({ children, className }: PageContainerProps) {
  return (
    <div className={cn(
      "min-h-screen bg-background",
      className
    )}>
      <div className="container mx-auto px-6 py-8 max-w-7xl">
        {children}
      </div>
    </div>
  );
}