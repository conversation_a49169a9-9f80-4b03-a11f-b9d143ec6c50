import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { StatsCard } from "@/components/dashboard/StatsCard";
import { ActivityFeed } from "@/components/dashboard/ActivityFeed";
import { PageHeader } from "@/components/layout/PageHeader";
import { PageContainer } from "@/components/layout/PageContainer";
import { 
  TrendingUp,
  TrendingDown,
  Users,
  ShoppingCart,
  DollarSign,
  Package,
  ArrowUpRight,
  Download,
  RefreshCw,
  BarChart3,
  Target,
  Globe,
  Star,
  Zap,
  AlertCircle,
  Eye,
  Calendar
} from "lucide-react";

// Mock data service - in production this would be real API calls
const fetchDashboardStats = async () => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 800));
  
  return {
    totalRevenue: 128450,
    totalOrders: 1247,
    totalCustomers: 3890,
    conversionRate: 3.8,
    avgOrderValue: 103.25,
    openTickets: 12,
    previousMonth: {
      totalRevenue: 115200,
      totalOrders: 1098,
      totalCustomers: 3654,
      conversionRate: 3.2
    }
  };
};

const fetchRecentOrders = async () => {
  await new Promise(resolve => setTimeout(resolve, 600));
  
  return [
    { id: "ORD-001", customer: "John Smith", amount: 156.99, status: "completed", time: "2 min ago" },
    { id: "ORD-002", customer: "Sarah Johnson", amount: 89.50, status: "processing", time: "8 min ago" },
    { id: "ORD-003", customer: "Mike Chen", amount: 234.75, status: "shipped", time: "15 min ago" },
    { id: "ORD-004", customer: "Emma Wilson", amount: 67.25, status: "pending", time: "32 min ago" },
  ];
};

const fetchTopProducts = async () => {
  await new Promise(resolve => setTimeout(resolve, 700));
  
  return [
    { id: 1, name: "Wireless Bluetooth Headphones", sales: 342, revenue: 25650, growth: 15.2 },
    { id: 2, name: "Smart Fitness Watch", sales: 298, revenue: 44700, growth: 23.1 },
    { id: 3, name: "Premium Coffee Subscription", sales: 156, revenue: 7800, growth: 8.7 },
    { id: 4, name: "Organic Skincare Set", sales: 134, revenue: 10720, growth: 12.3 },
  ];
};

// Loading skeleton component
const DashboardSkeleton = () => (
  <div className="space-y-6">
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {[...Array(4)].map((_, i) => (
        <Card key={i}>
          <CardContent className="p-6">
            <Skeleton className="h-4 w-24 mb-2" />
            <Skeleton className="h-8 w-16 mb-2" />
            <Skeleton className="h-3 w-20" />
          </CardContent>
        </Card>
      ))}
    </div>
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <Card className="lg:col-span-2">
        <CardHeader>
          <Skeleton className="h-6 w-32" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-40" />
                  <Skeleton className="h-3 w-24" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-24" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="flex justify-between items-center">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-4 w-16" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
);

const Dashboard = () => {
  const [refreshKey, setRefreshKey] = useState(0);

  const { data: stats, isLoading: statsLoading, error: statsError } = useQuery({
    queryKey: ['dashboard-stats', refreshKey],
    queryFn: fetchDashboardStats,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const { data: recentOrders, isLoading: ordersLoading } = useQuery({
    queryKey: ['recent-orders', refreshKey],
    queryFn: fetchRecentOrders,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  const { data: topProducts, isLoading: productsLoading } = useQuery({
    queryKey: ['top-products', refreshKey],
    queryFn: fetchTopProducts,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  const calculateChange = (current: number, previous: number): { value: string; type: 'positive' | 'negative' } => {
    const change = ((current - previous) / previous) * 100;
    return {
      value: `${change >= 0 ? '+' : ''}${change.toFixed(1)}%`,
      type: change >= 0 ? 'positive' : 'negative'
    };
  };

  if (statsLoading || ordersLoading || productsLoading) {
    return (
      <PageContainer>
        <PageHeader
          title="Dashboard"
          description="Monitor your business performance and key metrics"
        />
        <DashboardSkeleton />
      </PageContainer>
    );
  }

  if (statsError) {
    return (
      <PageContainer>
        <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
          <AlertCircle className="h-12 w-12 text-destructive" />
          <div className="text-center">
            <h3 className="text-lg font-semibold">Failed to load dashboard</h3>
            <p className="text-muted-foreground">Please try refreshing the page</p>
          </div>
          <Button onClick={handleRefresh} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      </PageContainer>
    );
  }

  const revenueChange = stats ? calculateChange(stats.totalRevenue, stats.previousMonth.totalRevenue) : { value: '0%', type: 'positive' as const };
  const ordersChange = stats ? calculateChange(stats.totalOrders, stats.previousMonth.totalOrders) : { value: '0%', type: 'positive' as const };
  const customersChange = stats ? calculateChange(stats.totalCustomers, stats.previousMonth.totalCustomers) : { value: '0%', type: 'positive' as const };
  const conversionChange = stats ? calculateChange(stats.conversionRate, stats.previousMonth.conversionRate) : { value: '0%', type: 'positive' as const };

  return (
    <PageContainer>
      <div className="flex flex-col space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
            <p className="text-muted-foreground">
              Welcome back! Here's what's happening with your business today.
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button onClick={handleRefresh} size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatsCard
            title="Total Revenue"
            value={`$${stats?.totalRevenue.toLocaleString() || '0'}`}
            change={revenueChange.value}
            changeType={revenueChange.type}
            icon={<DollarSign className="h-5 w-5" />}
            description="vs last month"
          />
          <StatsCard
            title="Total Orders"
            value={stats?.totalOrders.toString() || '0'}
            change={ordersChange.value}
            changeType={ordersChange.type}
            icon={<ShoppingCart className="h-5 w-5" />}
            description="vs last month"
          />
          <StatsCard
            title="Total Customers"
            value={stats?.totalCustomers.toString() || '0'}
            change={customersChange.value}
            changeType={customersChange.type}
            icon={<Users className="h-5 w-5" />}
            description="vs last month"
          />
          <StatsCard
            title="Conversion Rate"
            value={`${stats?.conversionRate || 0}%`}
            change={conversionChange.value}
            changeType={conversionChange.type}
            icon={<Target className="h-5 w-5" />}
            description="vs last month"
          />
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Activity */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Recent Orders
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentOrders?.map((order) => (
                  <div key={order.id} className="flex items-center justify-between p-4 rounded-lg border border-border/50 hover:bg-muted/50 transition-colors">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                        <ShoppingCart className="h-4 w-4 text-primary" />
                      </div>
                      <div>
                        <p className="font-medium">{order.id}</p>
                        <p className="text-sm text-muted-foreground">{order.customer}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">${order.amount}</p>
                      <div className="flex items-center gap-2">
                        <Badge variant={order.status === 'completed' ? 'default' : order.status === 'processing' ? 'secondary' : 'outline'}>
                          {order.status}
                        </Badge>
                        <span className="text-xs text-muted-foreground">{order.time}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-6">
                <Button variant="outline" className="w-full">
                  <Eye className="h-4 w-4 mr-2" />
                  View All Orders
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Top Products */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Top Products
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topProducts?.map((product, index) => (
                  <div key={product.id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center text-sm font-medium text-primary">
                        {index + 1}
                      </div>
                      <div>
                        <p className="font-medium text-sm leading-tight">{product.name}</p>
                        <p className="text-xs text-muted-foreground">{product.sales} sold</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-sm">${product.revenue.toLocaleString()}</p>
                      <p className="text-xs text-success">+{product.growth}%</p>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-6">
                <Button variant="outline" size="sm" className="w-full">
                  <ArrowUpRight className="h-4 w-4 mr-2" />
                  View All Products
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Additional Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Avg. Order Value</p>
                  <p className="text-2xl font-bold">${stats?.avgOrderValue.toFixed(2) || '0'}</p>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-50 rounded-xl flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Open Tickets</p>
                  <p className="text-2xl font-bold">{stats?.openTickets || 0}</p>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-orange-100 to-orange-50 rounded-xl flex items-center justify-center">
                  <AlertCircle className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Active Sessions</p>
                  <p className="text-2xl font-bold">247</p>
                  <div className="flex items-center gap-1 mt-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-xs text-muted-foreground">Live</span>
                  </div>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-green-100 to-green-50 rounded-xl flex items-center justify-center">
                  <Globe className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </PageContainer>
  );
};

export default Dashboard;