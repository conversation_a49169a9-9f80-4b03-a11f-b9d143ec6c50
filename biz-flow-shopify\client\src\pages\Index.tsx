import { Layout } from "@/components/Layout";
import { PageContainer } from "@/components/layout/PageContainer";
import { PageHeader } from "@/components/layout/PageHeader";
import { EnhancedDashboard } from "@/components/dashboard/EnhancedDashboard";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Calendar,
  Download,
  Plus,
  RefreshCw
} from "lucide-react";

const Index = () => {
  return (
    <Layout>
      <PageContainer>
        <PageHeader 
          title="Business Dashboard"
          description="Welcome back! Here's a comprehensive overview of your business performance and analytics."
          actions={
            <>
              <Button variant="outline" size="sm" className="gap-2">
                <Calendar className="w-4 h-4" />
                Last 30 days
              </Button>
              <Button variant="outline" size="sm" className="gap-2">
                <Download className="w-4 h-4" />
                Export Report
              </Button>
              <Button variant="outline" size="sm" className="gap-2">
                <RefreshCw className="w-4 h-4" />
                Refresh
              </Button>
              <Button size="sm" className="gap-2 bg-primary hover:bg-primary-hover">
                <Plus className="w-4 h-4" />
                Add Product
              </Button>
            </>
          }
        />
        
        <div className="dashboard-section">
          <EnhancedDashboard />
        </div>
      </PageContainer>
    </Layout>
  );
};

export default Index;