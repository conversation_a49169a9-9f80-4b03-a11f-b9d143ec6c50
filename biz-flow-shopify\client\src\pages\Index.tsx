import { useState, useEffect, useRef } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  TrendingUp,
  TrendingDown,
  Users,
  ShoppingCart,
  DollarSign,
  Package,
  ArrowUpRight,
  Download,
  RefreshCw,
  Target,
  Globe,
  AlertCircle,
  Calendar,
  Filter,
  MoreHorizontal,
  Search,
  Bell,
  Activity,
  Eye,
  Star,
  Clock,
  CheckCircle,
  XCircle,
  Zap,
  BarChart3
} from "lucide-react";

interface DashboardStats {
  metrics: {
    productRevenue: number;
    totalDeals: number;
    createdTickets: number;
    averageReply: number;
    revenueChange: number;
    dealsChange: number;
    ticketsChange: number;
    replyChange: number;
    conversionRate: number;
    avgOrderValue: number;
    activeUsers: number;
    bounceRate: number;
  };
  revenueChart: Array<{
    date: string;
    value: number;
    orders: number;
    visitors: number;
  }>;
  ticketChart: Array<{
    month: string;
    created: number;
    solved: number;
    avgTime: number;
  }>;
  recentActivity: Array<{
    id: string;
    type: string;
    title: string;
    customer: string;
    amount: string | null;
    time: string;
    status: string;
  }>;
  topProducts: Array<{
    id: number;
    name: string;
    category: string;
    sales: number;
    revenue: number;
    growth: string;
    rating: string;
    stock: number;
  }>;
}

// Enhanced data service with real-time simulation
const fetchDashboardStats = async (timeframe: string = "30d"): Promise<DashboardStats> => {
  await new Promise(resolve => setTimeout(resolve, 600 + Math.random() * 400));
  
  const baseMultiplier = timeframe === "7d" ? 0.3 : timeframe === "90d" ? 3 : 1;
  
  return {
    metrics: {
      productRevenue: Math.round(4250 * baseMultiplier * (0.9 + Math.random() * 0.2)),
      totalDeals: Math.round(1625 * baseMultiplier * (0.9 + Math.random() * 0.2)),
      createdTickets: Math.round(3452 * baseMultiplier * (0.9 + Math.random() * 0.2)),
      averageReply: 8.02 + (Math.random() - 0.5) * 2,
      revenueChange: 8.5 + (Math.random() - 0.5) * 4,
      dealsChange: -5.2 + (Math.random() - 0.5) * 6,
      ticketsChange: 16.3 + (Math.random() - 0.5) * 8,
      replyChange: 4.8 + (Math.random() - 0.5) * 3,
      conversionRate: 3.42 + (Math.random() - 0.5) * 1,
      avgOrderValue: 127.50 + (Math.random() - 0.5) * 40,
      activeUsers: 1247 + Math.round((Math.random() - 0.5) * 200),
      bounceRate: 42.3 + (Math.random() - 0.5) * 10
    },
    revenueChart: Array.from({ length: 30 }, (_, i) => ({
      date: new Date(2024, 9, i + 1).toISOString().split('T')[0],
      value: 2000 + Math.sin(i * 0.3) * 800 + Math.random() * 600 + i * 50,
      orders: Math.round(20 + Math.sin(i * 0.2) * 8 + Math.random() * 6),
      visitors: Math.round(150 + Math.sin(i * 0.4) * 60 + Math.random() * 40)
    })),
    ticketChart: Array.from({ length: 12 }, (_, i) => ({
      month: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'][i],
      created: Math.round(240 + Math.sin(i * 0.5) * 80 + Math.random() * 60),
      solved: Math.round(220 + Math.sin(i * 0.5) * 75 + Math.random() * 50),
      avgTime: Math.round(4.2 + Math.sin(i * 0.3) * 1.5 + Math.random() * 0.8)
    })),
    recentActivity: Array.from({ length: 8 }, (_, i) => ({
      id: `act-${i + 1}`,
      type: ['order', 'review', 'signup', 'refund', 'support'][Math.floor(Math.random() * 5)],
      title: [
        'New order received', 'Customer review posted', 'User signed up', 
        'Refund processed', 'Support ticket created', 'Payment confirmed',
        'Product viewed', 'Cart abandoned'
      ][Math.floor(Math.random() * 8)],
      customer: [
        'Sarah Johnson', 'Mike Chen', 'Emma Wilson', 'John Smith',
        'Lisa Brown', 'David Garcia', 'Anna Taylor', 'Tom Anderson'
      ][Math.floor(Math.random() * 8)],
      amount: Math.random() > 0.5 ? `$${(50 + Math.random() * 200).toFixed(2)}` : null,
      time: `${Math.floor(Math.random() * 60) + 1} min ago`,
      status: ['success', 'pending', 'warning'][Math.floor(Math.random() * 3)]
    })),
    topProducts: Array.from({ length: 6 }, (_, i) => ({
      id: i + 1,
      name: [
        'Wireless Bluetooth Headphones', 'Smart Fitness Watch', 'Premium Coffee Subscription',
        'Organic Skincare Set', 'Laptop Stand Pro', 'Wireless Phone Charger'
      ][i],
      category: ['Electronics', 'Fitness', 'Food & Beverage', 'Beauty', 'Accessories', 'Tech'][i],
      sales: Math.round(342 - i * 40 + Math.random() * 50),
      revenue: Math.round(25650 - i * 3000 + Math.random() * 2000),
      growth: (15.2 - i * 2 + Math.random() * 4).toFixed(1),
      rating: (4.2 + Math.random() * 0.8).toFixed(1),
      stock: Math.round(50 + Math.random() * 200)
    }))
  };
};

// Enhanced Metric Card Component
interface MetricCardProps {
  title: string;
  value: string;
  change: string;
  changeType: 'positive' | 'negative';
  icon: React.ComponentType<{ className?: string }>;
  subtitle: string;
  trend?: number[];
  onClick?: () => void;
}

const EnhancedMetricCard: React.FC<MetricCardProps> = ({ 
  title, 
  value, 
  change, 
  changeType, 
  icon: Icon, 
  subtitle, 
  trend = [],
  onClick 
}) => {
  const [isHovered, setIsHovered] = useState(false);
  
  return (
    <Card 
      className="group bg-white border-0 shadow-[0_2px_8px_rgba(0,0,0,0.04)] hover:shadow-[0_8px_25px_rgba(0,0,0,0.12)] transition-all duration-300 cursor-pointer overflow-hidden"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onClick}
    >
      <CardContent className="p-6 relative">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        
        <div className="flex items-start justify-between relative z-10">
          <div className="space-y-2 flex-1">
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium text-gray-500">{title}</p>
              {changeType === 'positive' && <TrendingUp className="w-3 h-3 text-emerald-500" />}
              {changeType === 'negative' && <TrendingDown className="w-3 h-3 text-red-500" />}
            </div>
            
            <div className="flex items-baseline space-x-3">
              <p className="text-2xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
                {value}
              </p>
              <div className={`flex items-center text-sm font-semibold px-2 py-1 rounded-full ${
                changeType === 'positive' 
                  ? 'text-emerald-700 bg-emerald-50 border border-emerald-200' 
                  : 'text-red-700 bg-red-50 border border-red-200'
              }`}>
                {changeType === 'positive' ? '+' : ''}{change}%
              </div>
            </div>
            
            <p className="text-xs text-gray-400">{subtitle}</p>
            
            {trend.length > 0 && (
              <div className="flex items-end space-x-1 mt-3">
                {trend.slice(-8).map((point: number, i: number) => (
                  <div
                    key={i}
                    className={`w-1 rounded-full transition-all duration-300 ${
                      changeType === 'positive' ? 'bg-emerald-400' : 'bg-red-400'
                    }`}
                    style={{ 
                      height: `${Math.max(4, (point / Math.max(...trend)) * 16)}px`,
                      opacity: isHovered ? 1 : 0.6
                    }}
                  />
                ))}
              </div>
            )}
          </div>
          
          <div className="p-3 bg-blue-50 rounded-xl group-hover:bg-blue-100 group-hover:scale-110 transition-all duration-300">
            <Icon className="w-5 h-5 text-blue-600" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Interactive Line Chart Component
interface LineChartProps {
  data: Array<{
    date: string;
    value: number;
    orders: number;
    visitors: number;
  }>;
  title: string;
  timeframe: string;
}

const InteractiveLineChart: React.FC<LineChartProps> = ({ data, title, timeframe }) => {
  const [tooltip, setTooltip] = useState<any>(null);
  const [hoveredPoint, setHoveredPoint] = useState<number | null>(null);
  const svgRef = useRef<SVGSVGElement>(null);

  const maxValue = Math.max(...data.map(d => d.value));
  const minValue = Math.min(...data.map(d => d.value));
  const range = maxValue - minValue;

  const createPath = () => {
    const width = 100;
    const height = 60;
    
    return data.map((point, index) => {
      const x = (index / (data.length - 1)) * width;
      const y = height - ((point.value - minValue) / range) * height;
      return `${index === 0 ? 'M' : 'L'} ${x} ${y}`;
    }).join(' ');
  };

  const handleMouseMove = (event: React.MouseEvent, index: number) => {
    if (svgRef.current) {
      const rect = svgRef.current.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;
      
      setTooltip({
        x,
        y,
        data: data[index]
      });
      setHoveredPoint(index);
    }
  };

  return (
    <Card className="bg-white border-0 shadow-[0_2px_8px_rgba(0,0,0,0.04)] hover:shadow-[0_4px_12px_rgba(0,0,0,0.08)] transition-all duration-300">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg font-semibold text-gray-900">{title}</CardTitle>
            <p className="text-sm text-gray-500 mt-1">Revenue performance over time</p>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-emerald-600 border-emerald-200 bg-emerald-50">
              <TrendingUp className="w-3 h-3 mr-1" />
              +12.5%
            </Badge>
            <Button variant="ghost" size="sm" className="h-8 px-3 text-gray-500 hover:text-gray-700">
              <MoreHorizontal className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-4 relative">
        <div className="h-64 relative">
          <svg 
            ref={svgRef}
            className="w-full h-full" 
            viewBox="0 0 100 60" 
            preserveAspectRatio="none"
            onMouseLeave={() => {
              setTooltip(null);
              setHoveredPoint(null);
            }}
          >
            <defs>
              <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor="#3B82F6" stopOpacity="0.2" />
                <stop offset="100%" stopColor="#3B82F6" stopOpacity="0" />
              </linearGradient>
            </defs>
            
            {[0, 20, 40, 60].map(y => (
              <line 
                key={y} 
                x1="0" 
                y1={y} 
                x2="100" 
                y2={y} 
                stroke="#f1f5f9" 
                strokeWidth="0.2"
              />
            ))}
            
            <path
              d={`${createPath()} L 100 60 L 0 60 Z`}
              fill="url(#gradient)"
            />
            
            <path
              d={createPath()}
              stroke="#3B82F6"
              strokeWidth="0.8"
              fill="none"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            
            {data.map((point, index) => {
              const x = (index / (data.length - 1)) * 100;
              const y = 60 - ((point.value - minValue) / range) * 60;
              
              return (
                <circle
                  key={index}
                  cx={x}
                  cy={y}
                  r={hoveredPoint === index ? "1.5" : "0.8"}
                  fill="#3B82F6"
                  stroke="white"
                  strokeWidth="0.3"
                  className="cursor-pointer transition-all duration-200 hover:r-2"
                  onMouseMove={(e) => handleMouseMove(e, index)}
                />
              );
            })}
          </svg>
          
          {tooltip && (
            <div 
              className="absolute z-20 bg-gray-900 text-white text-xs rounded-lg px-3 py-2 shadow-lg border border-gray-700 pointer-events-none transform -translate-x-1/2 -translate-y-full"
              style={{ left: `${tooltip.x}px`, top: `${tooltip.y - 10}px` }}
            >
              <div className="font-semibold">${(tooltip.data.value / 1000).toFixed(1)}k</div>
              <div className="text-gray-300">{tooltip.data.orders} orders</div>
              <div className="text-gray-300">{tooltip.data.visitors} visitors</div>
            </div>
          )}
          
          <div className="absolute inset-0 flex items-end justify-between text-xs text-gray-400 px-2 pb-2 pointer-events-none">
            <span>21 Oct</span>
            <span className="text-blue-600 font-medium flex items-center">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-1 animate-pulse" />
              Live
            </span>
            <span>21 Nov</span>
          </div>
        </div>
        
        <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-100">
          <div className="flex items-center space-x-6">
            <div className="text-sm">
              <span className="text-gray-500">Peak: </span>
              <span className="font-semibold text-gray-900">${(maxValue/1000).toFixed(1)}k</span>
            </div>
            <div className="text-sm">
              <span className="text-gray-500">Avg: </span>
              <span className="font-semibold text-gray-900">${(data.reduce((a, b) => a + b.value, 0) / data.length / 1000).toFixed(1)}k</span>
            </div>
          </div>
          <div className="flex items-center text-sm text-emerald-600 font-medium">
            <TrendingUp className="w-4 h-4 mr-1" />
            +12.5% vs last {timeframe === '7d' ? 'week' : timeframe === '30d' ? 'month' : 'quarter'}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Enhanced Activity Feed with real-time updates
const EnhancedActivityFeed = ({ activities }: any) => {
  const [filter, setFilter] = useState('all');
  
  const filteredActivities = activities.filter((activity: any) => 
    filter === 'all' || activity.type === filter
  );

  const getActivityIcon = (type: string) => {
    const icons = {
      order: ShoppingCart,
      review: Star,
      signup: Users,
      refund: XCircle,
      support: AlertCircle
    };
    const Icon = icons[type as keyof typeof icons] || Activity;
    return <Icon className="w-4 h-4" />;
  };

  const getStatusColor = (status: string) => {
    const colors = {
      success: 'bg-emerald-100 text-emerald-600 border-emerald-200',
      pending: 'bg-amber-100 text-amber-600 border-amber-200',
      warning: 'bg-red-100 text-red-600 border-red-200'
    };
    return colors[status as keyof typeof colors] || colors.success;
  };

  return (
    <Card className="bg-white border-0 shadow-[0_2px_8px_rgba(0,0,0,0.04)]">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg font-semibold text-gray-900 flex items-center">
              <Activity className="w-5 h-5 mr-2" />
              Recent Activity
              <div className="w-2 h-2 bg-emerald-500 rounded-full ml-2 animate-pulse" />
            </CardTitle>
            <p className="text-sm text-gray-500 mt-1">Live business activity feed</p>
          </div>
          <Select value={filter} onValueChange={setFilter}>
            <SelectTrigger className="w-32 h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="order">Orders</SelectItem>
              <SelectItem value="review">Reviews</SelectItem>
              <SelectItem value="signup">Signups</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3 max-h-96 overflow-y-auto custom-scrollbar">
          {filteredActivities.map((activity: any, index: number) => (
            <div
              key={activity.id}
              className="flex items-center space-x-4 p-3 rounded-lg border border-gray-100 hover:bg-gray-50 transition-all duration-200 group"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className={`w-10 h-10 rounded-full flex items-center justify-center border-2 ${getStatusColor(activity.status)} group-hover:scale-110 transition-transform duration-200`}>
                {getActivityIcon(activity.type)}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {activity.title}
                  </p>
                  <span className="text-xs text-gray-400 flex-shrink-0 ml-2">
                    {activity.time}
                  </span>
                </div>
                <div className="flex items-center justify-between mt-1">
                  <p className="text-sm text-gray-500 truncate">
                    {activity.customer}
                  </p>
                  {activity.amount && (
                    <span className="text-sm font-semibold text-gray-900">
                      {activity.amount}
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-6 pt-4 border-t border-gray-100">
          <Button variant="outline" className="w-full text-sm hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200 transition-all duration-200">
            <Eye className="w-4 h-4 mr-2" />
            View All Activity
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

// Enhanced Top Products with detailed information
const EnhancedTopProducts = ({ products }: any) => {
  const [sortBy, setSortBy] = useState('revenue');
  
  const sortedProducts = [...products].sort((a, b) => {
    if (sortBy === 'revenue') return b.revenue - a.revenue;
    if (sortBy === 'sales') return b.sales - a.sales;
    if (sortBy === 'rating') return parseFloat(b.rating) - parseFloat(a.rating);
    return 0;
  });

  return (
    <Card className="bg-white border-0 shadow-[0_2px_8px_rgba(0,0,0,0.04)]">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg font-semibold text-gray-900 flex items-center">
              <Package className="w-5 h-5 mr-2" />
              Top Products
            </CardTitle>
            <p className="text-sm text-gray-500 mt-1">Best performing products this month</p>
          </div>
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-32 h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="revenue">Revenue</SelectItem>
              <SelectItem value="sales">Sales</SelectItem>
              <SelectItem value="rating">Rating</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-4">
          {sortedProducts.map((product: any, index: number) => (
            <div 
              key={product.id} 
              className="flex items-center justify-between p-4 rounded-lg border border-gray-100 hover:border-blue-200 hover:bg-blue-50/30 transition-all duration-200 group cursor-pointer"
            >
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-50 rounded-xl flex items-center justify-center font-bold text-blue-600 group-hover:scale-110 transition-transform duration-200">
                    #{index + 1}
                  </div>
                  {index < 3 && (
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-amber-400 rounded-full flex items-center justify-center">
                      <Star className="w-2 h-2 text-white" fill="currentColor" />
                    </div>
                  )}
                </div>
                <div className="min-w-0 flex-1">
                  <div className="flex items-center space-x-2">
                    <p className="font-semibold text-gray-900 truncate">{product.name}</p>
                    <Badge variant="outline" className="text-xs">
                      {product.category}
                    </Badge>
                  </div>
                  <div className="flex items-center space-x-4 mt-1 text-sm text-gray-500">
                    <span>{product.sales} sold</span>
                    <span>•</span>
                    <div className="flex items-center">
                      <Star className="w-3 h-3 text-amber-400 mr-1" fill="currentColor" />
                      {product.rating}
                    </div>
                    <span>•</span>
                    <span className={product.stock < 50 ? 'text-red-500' : 'text-emerald-500'}>
                      {product.stock} in stock
                    </span>
                  </div>
                </div>
              </div>
              <div className="text-right">
                <p className="font-bold text-gray-900">${product.revenue.toLocaleString()}</p>
                <div className="flex items-center text-sm">
                  <TrendingUp className="w-3 h-3 text-emerald-500 mr-1" />
                  <span className="text-emerald-600 font-medium">+{product.growth}%</span>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-6 pt-4 border-t border-gray-100">
          <Button variant="outline" className="w-full text-sm hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200 transition-all duration-200">
            <ArrowUpRight className="w-4 h-4 mr-2" />
            View All Products
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

// Main Dashboard Component
const Dashboard = () => {
  const [timeframe, setTimeframe] = useState('30d');
  const [refreshKey, setRefreshKey] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [isRealTime, setIsRealTime] = useState(true);

  const { data: stats, isLoading, error } = useQuery({
    queryKey: ['dashboard-stats', refreshKey, timeframe],
    queryFn: () => fetchDashboardStats(timeframe),
    staleTime: 30 * 1000,
    refetchInterval: isRealTime ? 30000 : false,
  });

  useEffect(() => {
    if (isRealTime) {
      const interval = setInterval(() => {
        setRefreshKey(prev => prev + 1);
      }, 30000);
      return () => clearInterval(interval);
    }
  }, [isRealTime]);

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="animate-pulse space-y-8">
            <div className="flex justify-between items-center">
              <div className="space-y-2">
                <div className="h-8 bg-gray-200 rounded w-48"></div>
                <div className="h-4 bg-gray-200 rounded w-64"></div>
              </div>
              <div className="flex space-x-3">
                <div className="h-10 bg-gray-200 rounded w-32"></div>
                <div className="h-10 bg-gray-200 rounded w-32"></div>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="bg-white p-6 rounded-xl shadow-sm">
                  <div className="h-4 bg-gray-200 rounded w-24 mb-3"></div>
                  <div className="h-8 bg-gray-200 rounded w-16 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-20"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <Card className="max-w-md w-full mx-4">
          <CardContent className="p-8 text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Failed to load dashboard</h3>
            <p className="text-gray-600 mb-6">Please check your connection and try again</p>
            <Button onClick={handleRefresh} className="w-full">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!stats) return null;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Enhanced Header */}
        <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-8 space-y-4 lg:space-y-0">
          <div className="space-y-2">
            <div className="flex items-center space-x-3">
              <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
              <Badge variant="outline" className="bg-emerald-50 text-emerald-600 border-emerald-200">
                <div className="w-2 h-2 bg-emerald-500 rounded-full mr-2 animate-pulse" />
                Live
              </Badge>
            </div>
            <p className="text-gray-600">
              Welcome back! Here's what's happening with your business today.
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search dashboard..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 w-64 bg-white border-gray-200 focus:border-blue-300 focus:ring-blue-100"
              />
            </div>
            
            <Select value={timeframe} onValueChange={setTimeframe}>
              <SelectTrigger className="w-40 bg-white border-gray-200">
                <Calendar className="w-4 h-4 mr-2" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
              </SelectContent>
            </Select>
            
            <div className="flex items-center space-x-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setIsRealTime(!isRealTime)}
                className={isRealTime ? 'bg-blue-50 text-blue-600 border-blue-200' : ''}
              >
                <Zap className="w-4 h-4 mr-2" />
                {isRealTime ? 'Live' : 'Manual'}
              </Button>
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
              <Button onClick={handleRefresh} size="sm" className="bg-blue-600 hover:bg-blue-700">
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
        </div>

        {/* Enhanced Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <EnhancedMetricCard
            title="Product Revenue"
            value={`€${stats.metrics.productRevenue.toLocaleString()}`}
            change={stats.metrics.revenueChange.toFixed(1)}
            changeType={stats.metrics.revenueChange > 0 ? 'positive' : 'negative'}
            icon={DollarSign}
            subtitle="+ €1.246 Revenue"
            trend={stats.revenueChart.slice(-10).map(d => d.value)}
          />
          <EnhancedMetricCard
            title="Total Deals"
            value={stats.metrics.totalDeals.toLocaleString()}
            change={Math.abs(stats.metrics.dealsChange).toFixed(1)}
            changeType={stats.metrics.dealsChange > 0 ? 'positive' : 'negative'}
            icon={ShoppingCart}
            subtitle="+ 842 Deals"
            trend={stats.revenueChart.slice(-10).map(d => d.orders)}
          />
          <EnhancedMetricCard
            title="Created Tickets"
            value={stats.metrics.createdTickets.toLocaleString()}
            change={stats.metrics.ticketsChange.toFixed(1)}
            changeType={stats.metrics.ticketsChange > 0 ? 'positive' : 'negative'}
            icon={Package}
            subtitle="+ 1.023 Tickets"
            trend={stats.ticketChart.slice(-10).map(d => d.created)}
          />
          <EnhancedMetricCard
            title="Average Reply"
            value={`${stats.metrics.averageReply.toFixed(2)}:02`}
            change={stats.metrics.replyChange.toFixed(1)}
            changeType={stats.metrics.replyChange > 0 ? 'positive' : 'negative'}
            icon={Clock}
            subtitle="+ 0:40 Faster"
            trend={stats.ticketChart.slice(-10).map(d => d.avgTime)}
          />
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <InteractiveLineChart 
            data={stats.revenueChart} 
            title="Revenue Performance"
            timeframe={timeframe}
          />
          <Card className="bg-white border-0 shadow-[0_2px_8px_rgba(0,0,0,0.04)]">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-gray-900 flex items-center">
                <BarChart3 className="w-5 h-5 mr-2" />
                Key Metrics Overview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Conversion Rate</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.metrics.conversionRate.toFixed(2)}%</p>
                  </div>
                  <Badge className="bg-emerald-50 text-emerald-600 border-emerald-200">
                    +{(stats.metrics.conversionRate * 0.1).toFixed(1)}%
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Avg. Order Value</p>
                    <p className="text-2xl font-bold text-gray-900">${stats.metrics.avgOrderValue.toFixed(2)}</p>
                  </div>
                  <Badge className="bg-blue-50 text-blue-600 border-blue-200">
                    +{(stats.metrics.avgOrderValue * 0.05).toFixed(2)}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Active Users</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.metrics.activeUsers.toLocaleString()}</p>
                  </div>
                  <Badge className="bg-purple-50 text-purple-600 border-purple-200">
                    +{Math.round(stats.metrics.activeUsers * 0.08)}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Bounce Rate</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.metrics.bounceRate.toFixed(1)}%</p>
                  </div>
                  <Badge className="bg-amber-50 text-amber-600 border-amber-200">
                    -{(stats.metrics.bounceRate * 0.05).toFixed(1)}%
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Activity and Products Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <EnhancedActivityFeed activities={stats.recentActivity} />
          <EnhancedTopProducts products={stats.topProducts} />
        </div>

        {/* Pro Mode Enhancement */}
        <Card className="bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 border-0 text-white relative overflow-hidden">
          <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>
          <CardContent className="p-8 relative z-10">
            <div className="flex items-center justify-between">
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                    <Zap className="w-4 h-4 text-white" />
                  </div>
                  <h3 className="text-xl font-bold">Pro Mode</h3>
                  <Badge className="bg-white/20 text-white border-white/30">Premium</Badge>
                </div>
                <p className="text-blue-100 text-sm max-w-md">
                  Unlock advanced analytics, custom reports, real-time alerts, and AI-powered insights to supercharge your business growth.
                </p>
                <div className="flex items-center space-x-6 text-sm">
                  <div className="flex items-center">
                    <CheckCircle className="w-4 h-4 mr-2 text-emerald-300" />
                    AI Insights
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="w-4 h-4 mr-2 text-emerald-300" />
                    Custom Reports
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="w-4 h-4 mr-2 text-emerald-300" />
                    Real-time Alerts
                  </div>
                </div>
              </div>
              <div className="text-center space-y-3">
                <div className="text-3xl font-bold">$29</div>
                <div className="text-sm text-blue-200">per month</div>
                <Button className="bg-white text-blue-600 hover:bg-blue-50 font-semibold px-6 py-2">
                  Upgrade Now
                  <ArrowUpRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;