import { useState } from "react";
import { Layout } from "@/components/Layout";
import { PageContainer } from "@/components/layout/PageContainer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { 
  Sparkles, 
  Code, 
  Eye, 
  Download, 
  Plus,
  Zap,
  Palette,
  Settings,
  RefreshCw,
  Wand2,
  Brain,
  Globe,
  Cpu,
  Database,
  Shield,
  Layers,
  Terminal,
  Play,
  Pause,
  Square,
  Monitor,
  Smartphone,
  Tablet,
  Rocket,
  GitBranch,
  Share2,
  Copy,
  CheckCircle,
  Clock,
  AlertCircle,
  TrendingUp,
  Star,
  Heart,
  MessageSquare,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Pie<PERSON>hart,
  Activity,
  Wifi,
  CloudDownload,
  Save,
  FileText,
  Folder,
  Search,
  Filter,
  SortAsc,
  Grid,
  List,
  Maximize2
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface GenerationState {
  status: 'idle' | 'generating' | 'completed' | 'error';
  progress: number;
  currentStep: string;
  generatedCode: string;
  preview: string;
}

interface AppTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  framework: string;
  features: string[];
  complexity: 'basic' | 'intermediate' | 'advanced';
  gradient: string;
  icon: any;
  popular: boolean;
  rating: number;
  downloads: number;
}

const templates: AppTemplate[] = [
  {
    id: '1',
    name: 'SaaS Dashboard',
    description: 'Complete dashboard with analytics, user management, and billing',
    category: 'Business',
    framework: 'React + TypeScript',
    features: ['Authentication', 'Charts', 'User Management', 'Billing', 'Dark Mode'],
    complexity: 'advanced',
    gradient: 'from-blue-500/20 to-purple-500/20',
    icon: BarChart,
    popular: true,
    rating: 4.8,
    downloads: 15420
  },
  {
    id: '2',
    name: 'E-commerce Store',
    description: 'Modern online store with cart, payments, and inventory',
    category: 'E-commerce',
    framework: 'Next.js + Tailwind',
    features: ['Shopping Cart', 'Payment Integration', 'Product Management', 'Orders'],
    complexity: 'advanced',
    gradient: 'from-green-500/20 to-teal-500/20',
    icon: Globe,
    popular: true,
    rating: 4.9,
    downloads: 12350
  },
  {
    id: '3',
    name: 'Portfolio Site',
    description: 'Professional portfolio with projects showcase and contact form',
    category: 'Portfolio',
    framework: 'Vue.js + CSS',
    features: ['Project Gallery', 'Contact Form', 'Blog', 'SEO Optimized'],
    complexity: 'intermediate',
    gradient: 'from-orange-500/20 to-red-500/20',
    icon: Users,
    popular: false,
    rating: 4.5,
    downloads: 8920
  },
  {
    id: '4',
    name: 'Task Management',
    description: 'Collaborative task manager with real-time updates',
    category: 'Productivity',
    framework: 'React + TypeScript',
    features: ['Real-time Collaboration', 'Task Boards', 'File Sharing', 'Notifications'],
    complexity: 'intermediate',
    gradient: 'from-purple-500/20 to-pink-500/20',
    icon: CheckCircle,
    popular: true,
    rating: 4.7,
    downloads: 9850
  },
  {
    id: '5',
    name: 'Social Media App',
    description: 'Full-featured social platform with posts, messaging, and feeds',
    category: 'Social',
    framework: 'React + TypeScript',
    features: ['User Profiles', 'Posts & Feed', 'Real-time Chat', 'Media Upload'],
    complexity: 'advanced',
    gradient: 'from-pink-500/20 to-rose-500/20',
    icon: MessageSquare,
    popular: false,
    rating: 4.6,
    downloads: 7230
  },
  {
    id: '6',
    name: 'Blog Platform',
    description: 'Content management system with editor and publishing tools',
    category: 'Content',
    framework: 'Next.js + Tailwind',
    features: ['Rich Text Editor', 'SEO Tools', 'Comments', 'Analytics'],
    complexity: 'intermediate',
    gradient: 'from-cyan-500/20 to-blue-500/20',
    icon: FileText,
    popular: false,
    rating: 4.4,
    downloads: 6180
  }
];

export default function WebsiteBuilder() {
  const [generationState, setGenerationState] = useState<GenerationState>({
    status: 'idle',
    progress: 0,
    currentStep: '',
    generatedCode: '',
    preview: ''
  });

  const [formData, setFormData] = useState({
    description: '',
    appType: 'web-app',
    framework: 'react',
    complexity: 'intermediate',
    styling: 'tailwind',
    features: [] as string[],
    template: '',
    customizations: {
      includeAuth: false,
      includeDatabase: false,
      includeApi: false,
      includeTests: false,
      includeDarkMode: true,
      includeResponsive: true,
      includeAnimations: true,
      includeSeo: false
    }
  });

  const [activeTab, setActiveTab] = useState('create');
  const [selectedTemplate, setSelectedTemplate] = useState<AppTemplate | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filterCategory, setFilterCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  const { toast } = useToast();

  const handleGenerate = async () => {
    if (!formData.description.trim()) {
      toast({
        title: "Description Required",
        description: "Please describe your app idea to generate code.",
        variant: "destructive",
      });
      return;
    }

    setGenerationState({
      status: 'generating',
      progress: 0,
      currentStep: 'Analyzing requirements...',
      generatedCode: '',
      preview: ''
    });

    // Simulate AI generation process
    const steps = [
      'Analyzing requirements...',
      'Generating project structure...',
      'Creating UI components...',
      'Setting up routing...',
      'Implementing features...',
      'Adding styles and animations...',
      'Generating API endpoints...',
      'Optimizing and finalizing...'
    ];

    for (let i = 0; i < steps.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setGenerationState(prev => ({
        ...prev,
        progress: ((i + 1) / steps.length) * 100,
        currentStep: steps[i]
      }));
    }

    const mockCode = `import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Plus, Search, Filter, MoreHorizontal } from 'lucide-react';

export default function ${formData.appType === 'dashboard' ? 'Dashboard' : 'App'}() {
  const [items, setItems] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              ${formData.description.split(' ').slice(0, 3).join(' ')}
            </h1>
            <p className="text-muted-foreground mt-1">
              ${formData.description.substring(0, 100)}...
            </p>
          </div>
          <Button className="glass-button">
            <Plus className="w-4 h-4 mr-2" />
            Add New
          </Button>
        </div>

        <div className="grid gap-6">
          <Card className="glass-card">
            <CardHeader>
              <CardTitle>Main Content</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex gap-4">
                  <Input
                    placeholder="Search..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="glass-input"
                  />
                  <Button variant="outline">
                    <Search className="w-4 h-4" />
                  </Button>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {/* Generated content will go here */}
                  <div className="glass-card p-4">
                    <h3 className="font-semibold mb-2">Sample Item</h3>
                    <p className="text-sm text-muted-foreground">
                      This is a sample item generated by AI
                    </p>
                    <div className="flex gap-2 mt-3">
                      <Badge variant="secondary">Active</Badge>
                      <Badge variant="outline">New</Badge>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}`;

    setGenerationState({
      status: 'completed',
      progress: 100,
      currentStep: 'Generation completed!',
      generatedCode: mockCode,
      preview: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PC9kZWZzPjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjMDkwYTE0Ii8+PHJlY3QgeD0iMjAiIHk9IjIwIiB3aWR0aD0iMzYwIiBoZWlnaHQ9IjYwIiByeD0iOCIgZmlsbD0iIzE4MWEyYSIvPjx0ZXh0IHg9IjMwIiB5PSI1NSIgZmlsbD0iI2ZmZmZmZiIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE2cHgiIGZvbnQtd2VpZ2h0PSJib2xkIj5BcHAgVGl0bGU8L3RleHQ+PHJlY3QgeD0iMjAiIHk9IjEwMCIgd2lkdGg9IjE3MCIgaGVpZ2h0PSIxNDAiIHJ4PSI4IiBmaWxsPSIjMTgxYTJhIi8+PHJlY3QgeD0iMjEwIiB5PSIxMDAiIHdpZHRoPSIxNzAiIGhlaWdodD0iMTQwIiByeD0iOCIgZmlsbD0iIzE4MWEyYSIvPjx0ZXh0IHg9IjMwIiB5PSIxMzAiIGZpbGw9IiNmZmZmZmYiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNHB4Ij5DYXJEIENPUDAOE="'
    });

    toast({
      title: "App Generated Successfully!",
      description: "Your app has been generated and is ready for preview.",
    });
  };

  const handleTemplateSelect = (template: AppTemplate) => {
    setSelectedTemplate(template);
    setFormData(prev => ({
      ...prev,
      description: template.description,
      framework: template.framework.toLowerCase().includes('react') ? 'react' : 
                 template.framework.toLowerCase().includes('vue') ? 'vue' : 
                 template.framework.toLowerCase().includes('next') ? 'nextjs' : 'react',
      complexity: template.complexity,
      features: template.features
    }));
    setActiveTab('create');
  };

  const filteredTemplates = templates.filter(template => {
    const matchesCategory = filterCategory === 'all' || template.category.toLowerCase() === filterCategory;
    const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const categories = ['all', ...Array.from(new Set(templates.map(t => t.category.toLowerCase())))];

  return (
    <Layout>
      <PageContainer>
        {/* Modern Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-gradient-to-br from-primary/20 to-primary/10 rounded-xl flex items-center justify-center backdrop-blur-sm border border-primary/30">
              <Brain className="w-6 h-6 text-primary" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-foreground">AI App Builder</h1>
              <p className="text-foreground/60 mt-1">Generate production-ready applications with advanced AI</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm" className="glass-button">
              <GitBranch className="w-4 h-4 mr-2" />
              Version Control
            </Button>
            <Button variant="outline" size="sm" className="glass-button">
              <Share2 className="w-4 h-4 mr-2" />
              Share
            </Button>
            <Button size="sm" className="glass-button bg-primary/20 border-primary/30 text-primary hover:bg-primary/30">
              <Rocket className="w-4 h-4 mr-2" />
              Deploy
            </Button>
          </div>
        </div>

        {/* Advanced Tabs Interface */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="glass-card h-12 p-1 mb-8">
            <TabsTrigger value="create" className="glass-button">
              <Wand2 className="w-4 h-4 mr-2" />
              Create
            </TabsTrigger>
            <TabsTrigger value="templates" className="glass-button">
              <Layers className="w-4 h-4 mr-2" />
              Templates
            </TabsTrigger>
            <TabsTrigger value="code" className="glass-button">
              <Code className="w-4 h-4 mr-2" />
              Code
            </TabsTrigger>
            <TabsTrigger value="preview" className="glass-button">
              <Eye className="w-4 h-4 mr-2" />
              Preview
            </TabsTrigger>
            <TabsTrigger value="deploy" className="glass-button">
              <Rocket className="w-4 h-4 mr-2" />
              Deploy
            </TabsTrigger>
          </TabsList>

          {/* Create Tab */}
          <TabsContent value="create" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Main Generator Form */}
              <div className="lg:col-span-2 space-y-6">
                <div className="glass-card p-6">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="w-8 h-8 bg-primary/20 rounded-lg flex items-center justify-center">
                      <Brain className="w-4 h-4 text-primary" />
                    </div>
                    <h2 className="text-xl font-semibold text-foreground">AI App Generator</h2>
                  </div>
                  
                  <div className="space-y-6">
                    <div>
                      <Label htmlFor="description" className="text-sm font-medium text-foreground/80 mb-2 block">
                        Describe your app idea
                      </Label>
                      <Textarea
                        id="description"
                        placeholder="I want to build a modern task management app with real-time collaboration, kanban boards, file sharing, team messaging, and advanced analytics. The app should have a clean, minimal design with dark mode support and be mobile-responsive..."
                        value={formData.description}
                        onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                        className="glass-input min-h-32 resize-none"
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm font-medium text-foreground/80 mb-2 block">App Type</Label>
                        <select 
                          value={formData.appType}
                          onChange={(e) => setFormData(prev => ({ ...prev, appType: e.target.value }))}
                          className="glass-input w-full"
                        >
                          <option value="web-app">Web Application</option>
                          <option value="dashboard">Admin Dashboard</option>
                          <option value="ecommerce">E-commerce Store</option>
                          <option value="social">Social Media App</option>
                          <option value="portfolio">Portfolio Site</option>
                          <option value="blog">Blog Platform</option>
                          <option value="saas">SaaS Application</option>
                        </select>
                      </div>
                      
                      <div>
                        <Label className="text-sm font-medium text-foreground/80 mb-2 block">Framework</Label>
                        <select 
                          value={formData.framework}
                          onChange={(e) => setFormData(prev => ({ ...prev, framework: e.target.value }))}
                          className="glass-input w-full"
                        >
                          <option value="react">React + TypeScript</option>
                          <option value="nextjs">Next.js + TypeScript</option>
                          <option value="vue">Vue.js + TypeScript</option>
                          <option value="svelte">SvelteKit</option>
                          <option value="angular">Angular</option>
                          <option value="solid">SolidJS</option>
                        </select>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm font-medium text-foreground/80 mb-2 block">Complexity</Label>
                        <select 
                          value={formData.complexity}
                          onChange={(e) => setFormData(prev => ({ ...prev, complexity: e.target.value }))}
                          className="glass-input w-full"
                        >
                          <option value="basic">Basic - Simple UI and functionality</option>
                          <option value="intermediate">Intermediate - Medium complexity with features</option>
                          <option value="advanced">Advanced - Full-featured with integrations</option>
                        </select>
                      </div>
                      
                      <div>
                        <Label className="text-sm font-medium text-foreground/80 mb-2 block">Styling Framework</Label>
                        <select 
                          value={formData.styling}
                          onChange={(e) => setFormData(prev => ({ ...prev, styling: e.target.value }))}
                          className="glass-input w-full"
                        >
                          <option value="tailwind">Tailwind CSS</option>
                          <option value="styled-components">Styled Components</option>
                          <option value="emotion">Emotion</option>
                          <option value="css-modules">CSS Modules</option>
                          <option value="material-ui">Material-UI</option>
                          <option value="chakra">Chakra UI</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Advanced Customizations */}
                <div className="glass-card p-6">
                  <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
                    <Settings className="w-5 h-5" />
                    Advanced Customizations
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-sm font-medium text-foreground/80">Authentication</Label>
                          <p className="text-xs text-foreground/60">Include user login/signup</p>
                        </div>
                        <Switch 
                          checked={formData.customizations.includeAuth}
                          onCheckedChange={(checked) => setFormData(prev => ({
                            ...prev,
                            customizations: { ...prev.customizations, includeAuth: checked }
                          }))}
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-sm font-medium text-foreground/80">Database</Label>
                          <p className="text-xs text-foreground/60">Include database integration</p>
                        </div>
                        <Switch 
                          checked={formData.customizations.includeDatabase}
                          onCheckedChange={(checked) => setFormData(prev => ({
                            ...prev,
                            customizations: { ...prev.customizations, includeDatabase: checked }
                          }))}
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-sm font-medium text-foreground/80">REST API</Label>
                          <p className="text-xs text-foreground/60">Generate API endpoints</p>
                        </div>
                        <Switch 
                          checked={formData.customizations.includeApi}
                          onCheckedChange={(checked) => setFormData(prev => ({
                            ...prev,
                            customizations: { ...prev.customizations, includeApi: checked }
                          }))}
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-sm font-medium text-foreground/80">Unit Tests</Label>
                          <p className="text-xs text-foreground/60">Include testing framework</p>
                        </div>
                        <Switch 
                          checked={formData.customizations.includeTests}
                          onCheckedChange={(checked) => setFormData(prev => ({
                            ...prev,
                            customizations: { ...prev.customizations, includeTests: checked }
                          }))}
                        />
                      </div>
                    </div>
                    
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-sm font-medium text-foreground/80">Dark Mode</Label>
                          <p className="text-xs text-foreground/60">Theme switching support</p>
                        </div>
                        <Switch 
                          checked={formData.customizations.includeDarkMode}
                          onCheckedChange={(checked) => setFormData(prev => ({
                            ...prev,
                            customizations: { ...prev.customizations, includeDarkMode: checked }
                          }))}
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-sm font-medium text-foreground/80">Responsive Design</Label>
                          <p className="text-xs text-foreground/60">Mobile-first approach</p>
                        </div>
                        <Switch 
                          checked={formData.customizations.includeResponsive}
                          onCheckedChange={(checked) => setFormData(prev => ({
                            ...prev,
                            customizations: { ...prev.customizations, includeResponsive: checked }
                          }))}
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-sm font-medium text-foreground/80">Animations</Label>
                          <p className="text-xs text-foreground/60">Smooth transitions and effects</p>
                        </div>
                        <Switch 
                          checked={formData.customizations.includeAnimations}
                          onCheckedChange={(checked) => setFormData(prev => ({
                            ...prev,
                            customizations: { ...prev.customizations, includeAnimations: checked }
                          }))}
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-sm font-medium text-foreground/80">SEO Optimization</Label>
                          <p className="text-xs text-foreground/60">Meta tags and structured data</p>
                        </div>
                        <Switch 
                          checked={formData.customizations.includeSeo}
                          onCheckedChange={(checked) => setFormData(prev => ({
                            ...prev,
                            customizations: { ...prev.customizations, includeSeo: checked }
                          }))}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Generation Button */}
                <div className="glass-card p-6">
                  {generationState.status === 'generating' && (
                    <div className="space-y-4 mb-6">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-foreground/80">{generationState.currentStep}</span>
                        <span className="text-sm text-foreground/60">{Math.round(generationState.progress)}%</span>
                      </div>
                      <Progress value={generationState.progress} className="h-2" />
                    </div>
                  )}
                  
                  <div className="flex gap-3">
                    <Button 
                      onClick={handleGenerate}
                      disabled={generationState.status === 'generating'}
                      className="flex-1 glass-button bg-gradient-to-r from-primary/20 to-primary/10 border-primary/30 text-primary hover:from-primary/30 hover:to-primary/20"
                    >
                      {generationState.status === 'generating' ? (
                        <>
                          <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        <>
                          <Sparkles className="w-4 h-4 mr-2" />
                          Generate App
                        </>
                      )}
                    </Button>
                    <Button variant="outline" className="glass-button">
                      <Wand2 className="w-4 h-4 mr-2" />
                      Ideas
                    </Button>
                  </div>
                </div>
              </div>

              {/* Side Panel */}
              <div className="space-y-6">
                {/* AI Features */}
                <div className="glass-card p-6">
                  <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
                    <Zap className="w-5 h-5" />
                    AI Features
                  </h3>
                  
                  <div className="space-y-3">
                    <Button variant="outline" className="w-full justify-start glass-button">
                      <Cpu className="w-4 h-4 mr-2" />
                      Smart Component Generation
                    </Button>
                    <Button variant="outline" className="w-full justify-start glass-button">
                      <Database className="w-4 h-4 mr-2" />
                      Auto Database Schema
                    </Button>
                    <Button variant="outline" className="w-full justify-start glass-button">
                      <Shield className="w-4 h-4 mr-2" />
                      Security Best Practices
                    </Button>
                    <Button variant="outline" className="w-full justify-start glass-button">
                      <Palette className="w-4 h-4 mr-2" />
                      Design System Generator
                    </Button>
                  </div>
                </div>

                {/* Generation Stats */}
                <div className="glass-card p-6">
                  <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
                    <Activity className="w-5 h-5" />
                    Generation Stats
                  </h3>
                  
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-foreground/60">Total Apps Generated</span>
                      <span className="text-sm font-medium text-foreground">1,247</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-foreground/60">Success Rate</span>
                      <span className="text-sm font-medium text-green-400">98.5%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-foreground/60">Avg. Generation Time</span>
                      <span className="text-sm font-medium text-foreground">12s</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-foreground/60">Lines of Code</span>
                      <span className="text-sm font-medium text-foreground">2.3M+</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Templates Tab */}
          <TabsContent value="templates" className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-foreground/60" />
                  <Input
                    placeholder="Search templates..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="glass-input pl-10 w-80"
                  />
                </div>
                <div className="flex items-center gap-2">
                  <Filter className="w-4 h-4 text-foreground/60" />
                  <select 
                    value={filterCategory}
                    onChange={(e) => setFilterCategory(e.target.value)}
                    className="glass-input w-40"
                  >
                    {categories.map(cat => (
                      <option key={cat} value={cat}>
                        {cat.charAt(0).toUpperCase() + cat.slice(1)}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => setViewMode('grid')}
                  className={`glass-button ${viewMode === 'grid' ? 'bg-primary/20' : ''}`}
                >
                  <Grid className="w-4 h-4" />
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => setViewMode('list')}
                  className={`glass-button ${viewMode === 'list' ? 'bg-primary/20' : ''}`}
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>

            <div className={`grid gap-6 ${viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'}`}>
              {filteredTemplates.map((template) => {
                const IconComponent = template.icon;
                return (
                  <div key={template.id} className={`glass-card p-6 hover:border-primary/50 transition-all duration-200 cursor-pointer ${viewMode === 'list' ? 'flex items-center gap-6' : ''}`}>
                    <div className={`${viewMode === 'list' ? 'flex-shrink-0' : ''}`}>
                      <div className={`w-16 h-16 bg-gradient-to-br ${template.gradient} rounded-xl flex items-center justify-center mb-4 ${viewMode === 'list' ? 'mb-0' : ''}`}>
                        <IconComponent className="w-8 h-8 text-primary" />
                      </div>
                    </div>
                    
                    <div className={`${viewMode === 'list' ? 'flex-1' : ''}`}>
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="text-lg font-semibold text-foreground">{template.name}</h3>
                        <div className="flex items-center gap-2">
                          {template.popular && <Star className="w-4 h-4 text-yellow-500 fill-current" />}
                          <Badge variant="secondary" className="text-xs">{template.complexity}</Badge>
                        </div>
                      </div>
                      
                      <p className="text-sm text-foreground/60 mb-3">{template.description}</p>
                      
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-2 text-xs text-foreground/60">
                          <span>{template.framework}</span>
                          <span>•</span>
                          <span>{template.rating} ⭐</span>
                          <span>•</span>
                          <span>{template.downloads.toLocaleString()} downloads</span>
                        </div>
                      </div>
                      
                      <div className="flex flex-wrap gap-1 mb-4">
                        {template.features.slice(0, 3).map((feature, idx) => (
                          <Badge key={idx} variant="outline" className="text-xs">
                            {feature}
                          </Badge>
                        ))}
                        {template.features.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{template.features.length - 3} more
                          </Badge>
                        )}
                      </div>
                      
                      <div className="flex gap-2">
                        <Button 
                          size="sm" 
                          className="flex-1 glass-button bg-primary/20 border-primary/30 text-primary hover:bg-primary/30"
                          onClick={() => handleTemplateSelect(template)}
                        >
                          Use Template
                        </Button>
                        <Button variant="outline" size="sm" className="glass-button">
                          <Eye className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </TabsContent>

          {/* Code Tab */}
          <TabsContent value="code" className="space-y-6">
            <div className="glass-card p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                  <Code className="w-5 h-5" />
                  Generated Code
                </h3>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" className="glass-button">
                    <Copy className="w-4 h-4 mr-2" />
                    Copy
                  </Button>
                  <Button variant="outline" size="sm" className="glass-button">
                    <Download className="w-4 h-4 mr-2" />
                    Download
                  </Button>
                </div>
              </div>
              
              {generationState.status === 'completed' ? (
                <div className="bg-black/80 rounded-lg p-4 font-mono text-sm text-gray-300 h-96 overflow-auto">
                  <pre className="whitespace-pre-wrap">{generationState.generatedCode}</pre>
                </div>
              ) : (
                <div className="bg-black/80 rounded-lg p-4 h-96 flex items-center justify-center">
                  <div className="text-center">
                    <Code className="w-12 h-12 text-foreground/40 mx-auto mb-4" />
                    <p className="text-foreground/60">Generate an app to see the code</p>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          {/* Preview Tab */}
          <TabsContent value="preview" className="space-y-6">
            <div className="glass-card p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                  <Eye className="w-5 h-5" />
                  Live Preview
                </h3>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" className="glass-button">
                    <Monitor className="w-4 h-4" />
                  </Button>
                  <Button variant="outline" size="sm" className="glass-button">
                    <Tablet className="w-4 h-4" />
                  </Button>
                  <Button variant="outline" size="sm" className="glass-button">
                    <Smartphone className="w-4 h-4" />
                  </Button>
                  <Button variant="outline" size="sm" className="glass-button">
                    <Maximize2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              
              <div className="bg-white rounded-lg border h-96 flex items-center justify-center">
                {generationState.status === 'completed' ? (
                  <img 
                    src={generationState.preview} 
                    alt="App Preview" 
                    className="w-full h-full object-contain"
                  />
                ) : (
                  <div className="text-center">
                    <Monitor className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">Generate an app to see the preview</p>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          {/* Deploy Tab */}
          <TabsContent value="deploy" className="space-y-6">
            <div className="glass-card p-6">
              <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
                <Rocket className="w-5 h-5" />
                Deploy Your App
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="glass-card p-4 hover:border-primary/50 transition-colors cursor-pointer">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                      <CloudDownload className="w-5 h-5 text-blue-400" />
                    </div>
                    <div>
                      <h4 className="font-medium text-foreground">Vercel</h4>
                      <p className="text-xs text-foreground/60">Deploy instantly</p>
                    </div>
                  </div>
                  <Button size="sm" className="w-full glass-button">Deploy</Button>
                </div>
                
                <div className="glass-card p-4 hover:border-primary/50 transition-colors cursor-pointer">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
                      <CloudDownload className="w-5 h-5 text-purple-400" />
                    </div>
                    <div>
                      <h4 className="font-medium text-foreground">Netlify</h4>
                      <p className="text-xs text-foreground/60">Git-based deployment</p>
                    </div>
                  </div>
                  <Button size="sm" className="w-full glass-button">Deploy</Button>
                </div>
                
                <div className="glass-card p-4 hover:border-primary/50 transition-colors cursor-pointer">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
                      <CloudDownload className="w-5 h-5 text-green-400" />
                    </div>
                    <div>
                      <h4 className="font-medium text-foreground">Replit</h4>
                      <p className="text-xs text-foreground/60">Deploy on Replit</p>
                    </div>
                  </div>
                  <Button size="sm" className="w-full glass-button">Deploy</Button>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </PageContainer>
    </Layout>
  );
}