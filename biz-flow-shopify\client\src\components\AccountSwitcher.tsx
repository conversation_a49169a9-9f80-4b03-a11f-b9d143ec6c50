import { useState } from "react";
import { ChevronDown, Plus, Check, Building2, Settings, LogOut } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { CreateAccountModal } from "@/components/CreateAccountModal";
import { useToast } from "@/hooks/use-toast";

interface Account {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  type: 'personal' | 'business';
  isActive: boolean;
}

const mockAccounts: Account[] = [
  {
    id: '1',
    name: 'Personal Account',
    email: '<EMAIL>',
    type: 'personal',
    isActive: true,
  },
  {
    id: '2',
    name: 'Tech Startup Co.',
    email: '<EMAIL>',
    type: 'business',
    isActive: false,
  },
  {
    id: '3',
    name: 'Freelance Projects',
    email: '<EMAIL>',
    type: 'business',
    isActive: false,
  },
];

export function AccountSwitcher() {
  const [accounts, setAccounts] = useState<Account[]>(mockAccounts);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const activeAccount = accounts.find(account => account.isActive);
  const { toast } = useToast();

  const handleAccountSwitch = (accountId: string) => {
    const account = accounts.find(a => a.id === accountId);
    setAccounts(prev => 
      prev.map(account => ({
        ...account,
        isActive: account.id === accountId
      }))
    );
    
    if (account) {
      toast({
        title: "Account Switched",
        description: `Now using ${account.name}`,
      });
    }
  };

  const handleCreateAccount = () => {
    setIsCreateModalOpen(true);
  };

  const handleAccountCreated = (newAccount: Account) => {
    setAccounts(prev => [...prev, newAccount]);
  };

  const getAccountIcon = (type: string) => {
    return type === 'business' ? Building2 : Settings;
  };

  return (
    <>
      <CreateAccountModal 
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onAccountCreated={handleAccountCreated}
      />
      
      <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          className="glass-button h-10 px-3 py-2 flex items-center gap-2 text-sm hover:bg-primary/10"
        >
          <Avatar className="w-6 h-6">
            <AvatarFallback className="bg-primary/20 text-primary text-xs font-medium">
              {activeAccount?.name.charAt(0) || 'U'}
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col items-start">
            <span className="font-medium text-foreground">
              {activeAccount?.name || 'Select Account'}
            </span>
            <span className="text-xs text-foreground/60">
              {activeAccount?.email || 'No account selected'}
            </span>
          </div>
          <ChevronDown className="w-4 h-4 text-foreground/60" />
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent 
        className="w-72 glass-card border-primary/20"
        align="end"
        side="bottom"
      >
        <DropdownMenuLabel className="text-foreground/80 text-sm font-medium">
          Switch Account
        </DropdownMenuLabel>
        <DropdownMenuSeparator className="bg-primary/10" />
        
        {accounts.map((account) => {
          const IconComponent = getAccountIcon(account.type);
          return (
            <DropdownMenuItem 
              key={account.id}
              className="p-3 cursor-pointer hover:bg-primary/10 transition-colors duration-200"
              onClick={() => handleAccountSwitch(account.id)}
            >
              <div className="flex items-center gap-3 w-full">
                <Avatar className="w-8 h-8">
                  <AvatarFallback className="bg-primary/20 text-primary text-xs font-medium">
                    {account.name.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-foreground text-sm truncate">
                      {account.name}
                    </span>
                    <IconComponent className="w-3 h-3 text-foreground/60" />
                  </div>
                  <span className="text-xs text-foreground/60 truncate">
                    {account.email}
                  </span>
                </div>
                
                {account.isActive && (
                  <Check className="w-4 h-4 text-primary" />
                )}
              </div>
            </DropdownMenuItem>
          );
        })}
        
        <DropdownMenuSeparator className="bg-primary/10" />
        
        <DropdownMenuItem 
          className="p-3 cursor-pointer hover:bg-primary/10 transition-colors duration-200"
          onClick={handleCreateAccount}
        >
          <div className="flex items-center gap-3 w-full">
            <div className="w-8 h-8 bg-primary/20 rounded-full flex items-center justify-center">
              <Plus className="w-4 h-4 text-primary" />
            </div>
            <div className="flex-1">
              <span className="font-medium text-foreground text-sm">
                Add New Account
              </span>
              <div className="text-xs text-foreground/60">
                Create or connect another account
              </div>
            </div>
          </div>
        </DropdownMenuItem>
        
        <DropdownMenuSeparator className="bg-primary/10" />
        
        <DropdownMenuItem className="p-3 cursor-pointer hover:bg-red-500/10 transition-colors duration-200 text-red-400">
          <div className="flex items-center gap-3 w-full">
            <LogOut className="w-4 h-4" />
            <span className="font-medium text-sm">Sign Out</span>
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
    </>
  );
}