# replit.md

## Overview

This is a full-stack web application built with React, TypeScript, and Express.js. It features a modern business dashboard with a comprehensive UI component library, database integration via Drizzle ORM, and a clean architecture separating client, server, and shared code.

## User Preferences

Preferred communication style: Simple, everyday language.

## Recent Changes

### January 2025 - Enhanced Business Dashboard System
- **Major Architecture Enhancement**: Migrated from basic dashboard to comprehensive business management system
- **Data Model Expansion**: Added comprehensive schemas for users, products, customers, orders, order items, support tickets, and analytics
- **API Enhancement**: Created full REST API with CRUD operations for all business entities
- **UI/UX Modernization**: Implemented modern design system with enhanced components, responsive layouts, and improved styling
- **Dashboard Features**: Added tabbed dashboard with overview, analytics, customer management, and product management sections
- **Real-time Data**: Connected dashboard to live API endpoints with proper error handling and loading states
- **Enhanced Storage**: Improved in-memory storage with sample data and comprehensive business operations
- **AI App Builder**: Transformed Website Builder into AI App Builder with AI-powered generation capabilities, featuring app description input, framework selection, and AI-powered code generation interface
- **Layout Modernization**: Completed comprehensive layout restructuring with modern, minimal, clean architecture using reusable MetricCard components
- **Code Cleanup**: Removed verbose Card components and streamlined all layout components for better maintainability
- **Error Handling**: Fixed critical data handling errors across all pages with proper null checks and safe data filtering
- **Black & Blue Theme**: Implemented electric blue and black color scheme with deep black backgrounds, electric blue primaries, and enhanced visual hierarchy
- **Component Structure**: Fixed component imports and improved element structure across all pages for better consistency
- **Glass Morphism Design**: Implemented comprehensive glass morphism design with backdrop blur effects, rounded corners, and modern glass aesthetics
- **UI Modernization**: Updated all components to use glass design patterns with modern spacing, improved hover effects, and enhanced visual hierarchy
- **Accessibility Color Contrast Checker**: Added comprehensive accessibility tool for checking WCAG compliance with color contrast ratios, design system integration, and real-time preview

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Styling**: Tailwind CSS with shadcn/ui component library
- **State Management**: TanStack Query for server state management
- **Routing**: React Router for client-side navigation
- **Build Tool**: Vite with custom configuration for development and production
- **UI Components**: Comprehensive component library based on Radix UI primitives

### Backend Architecture
- **Runtime**: Node.js with Express.js framework
- **Language**: TypeScript with ES modules
- **Database**: PostgreSQL with Drizzle ORM for type-safe database operations
- **Session Management**: PostgreSQL session store with connect-pg-simple
- **Development**: Hot module replacement via Vite middleware integration

### Database Layer
- **ORM**: Drizzle ORM with PostgreSQL dialect
- **Schema**: Centralized schema definitions in `/shared/schema.ts`
- **Migrations**: Database migrations managed through Drizzle Kit
- **Connection**: Neon Database serverless connection for production

## Key Components

### Client Application
- **Dashboard Pages**: Comprehensive business dashboard with analytics, orders, products, customers
- **AI App Builder**: Visual website and application creation interface
- **Accessibility Checker**: WCAG compliance tool for color contrast validation with design system integration
- **Support Center**: Customer support and ticketing system
- **Settings**: User preferences and system configuration

### Server Components
- **API Routes**: RESTful API endpoints with Express.js
- **Storage Interface**: Abstracted storage layer with in-memory fallback
- **Middleware**: Request logging, error handling, and development utilities
- **Session Management**: Secure session handling with PostgreSQL storage

### Shared Components
- **Database Schema**: Type-safe database schema definitions
- **Type Definitions**: Shared TypeScript interfaces and types
- **Validation**: Zod schema validation for API requests

## Data Flow

1. **Client Requests**: React components make API calls using TanStack Query
2. **Server Processing**: Express.js routes handle requests and interact with storage layer
3. **Database Operations**: Drizzle ORM provides type-safe database queries
4. **Response Handling**: Structured JSON responses with proper error handling
5. **Client Updates**: TanStack Query manages cache invalidation and UI updates

## External Dependencies

### Core Dependencies
- **@neondatabase/serverless**: PostgreSQL serverless driver
- **@tanstack/react-query**: Server state management
- **drizzle-orm**: Type-safe database ORM
- **react-router-dom**: Client-side routing
- **zod**: Runtime type validation

### UI Dependencies
- **@radix-ui/***: Accessible UI primitive components
- **tailwindcss**: Utility-first CSS framework
- **lucide-react**: Icon library
- **recharts**: Data visualization components

### Development Dependencies
- **vite**: Fast build tool and development server
- **typescript**: Static type checking
- **drizzle-kit**: Database migrations and schema management

## Deployment Strategy

### Development Mode
- **Hot Reloading**: Vite development server with HMR
- **API Integration**: Express server integrated with Vite middleware
- **Database**: Local PostgreSQL or Neon Database connection
- **Error Handling**: Runtime error overlay for debugging

### Production Build
- **Client Bundle**: Vite builds optimized React application
- **Server Bundle**: esbuild creates production Express server
- **Static Assets**: Client files served from `/dist/public`
- **Database**: Production PostgreSQL with connection pooling

### Environment Configuration
- **DATABASE_URL**: PostgreSQL connection string (required)
- **NODE_ENV**: Environment flag for development/production modes
- **Session Configuration**: Secure session management with PostgreSQL store

The application follows a modern full-stack architecture with clear separation of concerns, type safety throughout the stack, and a focus on developer experience with hot reloading and comprehensive error handling.