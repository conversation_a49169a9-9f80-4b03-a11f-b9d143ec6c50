import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface StatusBadgeProps {
  status: string;
  variant?: "default" | "compact";
  className?: string;
}

const statusStyles = {
  // Order statuses
  pending: "status-pending",
  processing: "status-processing", 
  completed: "status-completed",
  shipped: "status-completed",
  delivered: "status-completed",
  cancelled: "status-inactive",
  
  // Payment statuses
  paid: "status-completed",
  unpaid: "status-pending",
  refunded: "status-inactive",
  
  // General statuses
  active: "status-active",
  inactive: "status-inactive",
  draft: "status-draft",
} as const;

export function StatusBadge({ status, variant = "default", className }: StatusBadgeProps) {
  const normalizedStatus = status.toLowerCase();
  const statusClass = statusStyles[normalizedStatus as keyof typeof statusStyles] || "status-draft";
  
  return (
    <Badge 
      className={cn(
        "border font-medium",
        statusClass,
        variant === "compact" && "px-2 py-1 text-xs",
        className
      )}
    >
      {status}
    </Badge>
  );
}