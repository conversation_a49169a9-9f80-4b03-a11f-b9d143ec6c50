import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowUpRight } from "lucide-react";

interface ActivityItem {
  id: string;
  type: "order" | "review" | "payment" | "customer";
  title: string;
  description: string;
  time: string;
  status: "success" | "warning" | "info" | "neutral";
}

const activities: ActivityItem[] = [
  {
    id: "1",
    type: "order",
    title: "New order received",
    description: "Order #3210 - Dashboard Template - $89.00",
    time: "2 minutes ago",
    status: "success"
  },
  {
    id: "2", 
    type: "review",
    title: "Customer review posted",
    description: "5-star review for UI Kit Pro",
    time: "15 minutes ago",
    status: "info"
  },
  {
    id: "3",
    type: "payment",
    title: "Payment processed",
    description: "PayPal payment - Order #3209",
    time: "1 hour ago",
    status: "warning"
  },
  {
    id: "4",
    type: "customer",
    title: "New customer registered",
    description: "<EMAIL> joined",
    time: "2 hours ago",
    status: "neutral"
  }
];

const statusStyles = {
  success: "w-2 h-2 bg-success rounded-full",
  warning: "w-2 h-2 bg-warning rounded-full", 
  info: "w-2 h-2 bg-info rounded-full",
  neutral: "w-2 h-2 bg-muted-foreground rounded-full"
};

export function ActivityFeed() {
  return (
    <div className="space-y-4">
      <div className="space-y-3">
        {activities.map((activity) => (
          <div
            key={activity.id}
            className="flex items-start gap-3 p-3 rounded-lg bg-muted/20 hover:bg-muted/30 transition-colors duration-200"
          >
            <div className={`${statusStyles[activity.status]} mt-2 flex-shrink-0`}></div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-foreground truncate">
                {activity.title}
              </p>
              <p className="text-xs text-muted-foreground mt-0.5 line-clamp-2">
                {activity.description}
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                {activity.time}
              </p>
            </div>
          </div>
        ))}
      </div>
      
      <Button variant="outline" className="w-full h-9 text-xs gap-2">
        View All Activity
        <ArrowUpRight className="w-3 h-3" />
      </Button>
    </div>
  );
}