import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowUpRight, ShoppingCart, User, Star, AlertCircle, Package } from "lucide-react";

interface ActivityItem {
  id: string;
  type: "order" | "review" | "customer" | "product" | "alert";
  title: string;
  description: string;
  time: string;
  status: "success" | "warning" | "info" | "error";
  amount?: string;
}

const activities: ActivityItem[] = [
  {
    id: "1",
    type: "order",
    title: "New order received",
    description: "Order #3210 from <PERSON>",
    time: "2 minutes ago",
    status: "success",
    amount: "$156.99"
  },
  {
    id: "2", 
    type: "review",
    title: "5-star review posted",
    description: "Customer loved the Wireless Headphones Pro",
    time: "15 minutes ago",
    status: "success"
  },
  {
    id: "3",
    type: "customer",
    title: "New customer registered",
    description: "<EMAIL> joined",
    time: "32 minutes ago",
    status: "info"
  },
  {
    id: "4",
    type: "product",
    title: "Low stock alert",
    description: "Smart Fitness Watch - only 5 left",
    time: "1 hour ago",
    status: "warning"
  },
  {
    id: "5",
    type: "order",
    title: "Payment confirmed",
    description: "Order #3209 payment processed",
    time: "2 hours ago",
    status: "success",
    amount: "$89.50"
  }
];

const getActivityIcon = (type: string) => {
  switch (type) {
    case "order":
      return <ShoppingCart className="h-4 w-4" />;
    case "review":
      return <Star className="h-4 w-4" />;
    case "customer":
      return <User className="h-4 w-4" />;
    case "product":
      return <Package className="h-4 w-4" />;
    case "alert":
      return <AlertCircle className="h-4 w-4" />;
    default:
      return <ShoppingCart className="h-4 w-4" />;
  }
};

const getStatusStyles = (status: string) => {
  switch (status) {
    case "success":
      return "bg-emerald-100 text-emerald-600 border-emerald-200";
    case "warning":
      return "bg-amber-100 text-amber-600 border-amber-200";
    case "info":
      return "bg-blue-100 text-blue-600 border-blue-200";
    case "error":
      return "bg-red-100 text-red-600 border-red-200";
    default:
      return "bg-gray-100 text-gray-600 border-gray-200";
  }
};

export function ActivityFeed() {
  return (
    <div className="space-y-4">
      {activities.map((activity) => (
        <div
          key={activity.id}
          className="flex items-center space-x-4 p-4 rounded-lg border border-border/50 hover:bg-muted/30 transition-colors"
        >
          <div className={`w-10 h-10 rounded-full flex items-center justify-center border ${getStatusStyles(activity.status)}`}>
            {getActivityIcon(activity.type)}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium text-foreground truncate">
                {activity.title}
              </p>
              <span className="text-xs text-muted-foreground flex-shrink-0 ml-2">
                {activity.time}
              </span>
            </div>
            <p className="text-sm text-muted-foreground truncate">
              {activity.description}
            </p>
          </div>
          
          {activity.amount && (
            <div className="text-sm font-semibold text-foreground">
              {activity.amount}
            </div>
          )}
        </div>
      ))}
      
      <div className="pt-4 border-t border-border/50">
        <Button variant="outline" size="sm" className="w-full">
          <ArrowUpRight className="h-4 w-4 mr-2" />
          View All Activity
        </Button>
      </div>
    </div>
  );
}