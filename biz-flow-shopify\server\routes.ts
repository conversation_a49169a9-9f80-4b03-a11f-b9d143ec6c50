import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { 
  insertUserSchema, updateUserSchema,
  insertProductSchema, updateProductSchema,
  insertCustomerSchema, updateCustomerSchema,
  insertOrderSchema, updateOrderSchema,
  insertOrderItemSchema,
  insertSupportTicketSchema, updateSupportTicketSchema,
} from "@shared/schema";
import { z } from "zod";

// Error handling middleware
const asyncHandler = (fn: Function) => (req: any, res: any, next: any) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Response helpers
const sendSuccess = (res: any, data: any, message?: string) => {
  res.json({ success: true, data, message });
};

const sendError = (res: any, message: string, status = 400) => {
  res.status(status).json({ success: false, error: message });
};

// Validation middleware
const validateSchema = (schema: z.ZodSchema) => (req: any, res: any, next: any) => {
  try {
    req.validatedData = schema.parse(req.body);
    next();
  } catch (error) {
    if (error instanceof z.ZodError) {
      return sendError(res, `Validation error: ${error.errors.map(e => e.message).join(', ')}`);
    }
    return sendError(res, 'Invalid request data');
  }
};

export async function registerRoutes(app: Express): Promise<Server> {
  // Health check endpoint
  app.get('/api/health', (req, res) => {
    sendSuccess(res, { status: 'healthy', timestamp: new Date() });
  });

  // User Routes
  app.get('/api/users/:id', asyncHandler(async (req: any, res: any) => {
    const user = await storage.getUser(parseInt(req.params.id));
    if (!user) return sendError(res, 'User not found', 404);
    sendSuccess(res, user);
  }));

  app.get('/api/users/username/:username', asyncHandler(async (req: any, res: any) => {
    const user = await storage.getUserByUsername(req.params.username);
    if (!user) return sendError(res, 'User not found', 404);
    sendSuccess(res, user);
  }));

  app.post('/api/users', validateSchema(insertUserSchema), asyncHandler(async (req: any, res: any) => {
    const user = await storage.createUser(req.validatedData);
    sendSuccess(res, user, 'User created successfully');
  }));

  app.put('/api/users/:id', validateSchema(updateUserSchema), asyncHandler(async (req: any, res: any) => {
    const user = await storage.updateUser(parseInt(req.params.id), req.validatedData);
    if (!user) return sendError(res, 'User not found', 404);
    sendSuccess(res, user, 'User updated successfully');
  }));

  app.delete('/api/users/:id', asyncHandler(async (req: any, res: any) => {
    const deleted = await storage.deleteUser(parseInt(req.params.id));
    if (!deleted) return sendError(res, 'User not found', 404);
    sendSuccess(res, null, 'User deleted successfully');
  }));

  // Product Routes
  app.get('/api/products', asyncHandler(async (req: any, res: any) => {
    const { category, status } = req.query;
    const products = await storage.getProducts({ category, status });
    sendSuccess(res, products);
  }));

  app.get('/api/products/:id', asyncHandler(async (req: any, res: any) => {
    const product = await storage.getProduct(parseInt(req.params.id));
    if (!product) return sendError(res, 'Product not found', 404);
    sendSuccess(res, product);
  }));

  app.post('/api/products', validateSchema(insertProductSchema), asyncHandler(async (req: any, res: any) => {
    const product = await storage.createProduct(req.validatedData);
    sendSuccess(res, product, 'Product created successfully');
  }));

  app.put('/api/products/:id', validateSchema(updateProductSchema), asyncHandler(async (req: any, res: any) => {
    const product = await storage.updateProduct(parseInt(req.params.id), req.validatedData);
    if (!product) return sendError(res, 'Product not found', 404);
    sendSuccess(res, product, 'Product updated successfully');
  }));

  app.delete('/api/products/:id', asyncHandler(async (req: any, res: any) => {
    const deleted = await storage.deleteProduct(parseInt(req.params.id));
    if (!deleted) return sendError(res, 'Product not found', 404);
    sendSuccess(res, null, 'Product deleted successfully');
  }));

  // Customer Routes
  app.get('/api/customers', asyncHandler(async (req: any, res: any) => {
    const { tier, status } = req.query;
    const customers = await storage.getCustomers({ tier, status });
    sendSuccess(res, customers);
  }));

  app.get('/api/customers/:id', asyncHandler(async (req: any, res: any) => {
    const customer = await storage.getCustomer(parseInt(req.params.id));
    if (!customer) return sendError(res, 'Customer not found', 404);
    sendSuccess(res, customer);
  }));

  app.get('/api/customers/email/:email', asyncHandler(async (req: any, res: any) => {
    const customer = await storage.getCustomerByEmail(req.params.email);
    if (!customer) return sendError(res, 'Customer not found', 404);
    sendSuccess(res, customer);
  }));

  app.post('/api/customers', validateSchema(insertCustomerSchema), asyncHandler(async (req: any, res: any) => {
    const customer = await storage.createCustomer(req.validatedData);
    sendSuccess(res, customer, 'Customer created successfully');
  }));

  app.put('/api/customers/:id', validateSchema(updateCustomerSchema), asyncHandler(async (req: any, res: any) => {
    const customer = await storage.updateCustomer(parseInt(req.params.id), req.validatedData);
    if (!customer) return sendError(res, 'Customer not found', 404);
    sendSuccess(res, customer, 'Customer updated successfully');
  }));

  app.delete('/api/customers/:id', asyncHandler(async (req: any, res: any) => {
    const deleted = await storage.deleteCustomer(parseInt(req.params.id));
    if (!deleted) return sendError(res, 'Customer not found', 404);
    sendSuccess(res, null, 'Customer deleted successfully');
  }));

  // Order Routes
  app.get('/api/orders', asyncHandler(async (req: any, res: any) => {
    const { customerId, status } = req.query;
    const orders = await storage.getOrders({ 
      customerId: customerId ? parseInt(customerId) : undefined, 
      status 
    });
    sendSuccess(res, orders);
  }));

  app.get('/api/orders/:id', asyncHandler(async (req: any, res: any) => {
    const order = await storage.getOrder(parseInt(req.params.id));
    if (!order) return sendError(res, 'Order not found', 404);
    sendSuccess(res, order);
  }));

  app.get('/api/orders/number/:orderNumber', asyncHandler(async (req: any, res: any) => {
    const order = await storage.getOrderByNumber(req.params.orderNumber);
    if (!order) return sendError(res, 'Order not found', 404);
    sendSuccess(res, order);
  }));

  app.post('/api/orders', validateSchema(insertOrderSchema), asyncHandler(async (req: any, res: any) => {
    const order = await storage.createOrder(req.validatedData);
    sendSuccess(res, order, 'Order created successfully');
  }));

  app.put('/api/orders/:id', validateSchema(updateOrderSchema), asyncHandler(async (req: any, res: any) => {
    const order = await storage.updateOrder(parseInt(req.params.id), req.validatedData);
    if (!order) return sendError(res, 'Order not found', 404);
    sendSuccess(res, order, 'Order updated successfully');
  }));

  app.delete('/api/orders/:id', asyncHandler(async (req: any, res: any) => {
    const deleted = await storage.deleteOrder(parseInt(req.params.id));
    if (!deleted) return sendError(res, 'Order not found', 404);
    sendSuccess(res, null, 'Order deleted successfully');
  }));

  // Order Items Routes
  app.get('/api/orders/:orderId/items', asyncHandler(async (req: any, res: any) => {
    const items = await storage.getOrderItems(parseInt(req.params.orderId));
    sendSuccess(res, items);
  }));

  app.post('/api/orders/:orderId/items', validateSchema(insertOrderItemSchema), asyncHandler(async (req: any, res: any) => {
    const item = await storage.addOrderItem({
      ...req.validatedData,
      orderId: parseInt(req.params.orderId)
    });
    sendSuccess(res, item, 'Order item added successfully');
  }));

  app.put('/api/order-items/:id', asyncHandler(async (req: any, res: any) => {
    const item = await storage.updateOrderItem(parseInt(req.params.id), req.body);
    if (!item) return sendError(res, 'Order item not found', 404);
    sendSuccess(res, item, 'Order item updated successfully');
  }));

  app.delete('/api/order-items/:id', asyncHandler(async (req: any, res: any) => {
    const deleted = await storage.deleteOrderItem(parseInt(req.params.id));
    if (!deleted) return sendError(res, 'Order item not found', 404);
    sendSuccess(res, null, 'Order item deleted successfully');
  }));

  // Support Ticket Routes
  app.get('/api/support-tickets', asyncHandler(async (req: any, res: any) => {
    const { customerId, status } = req.query;
    const tickets = await storage.getSupportTickets({ 
      customerId: customerId ? parseInt(customerId) : undefined, 
      status 
    });
    sendSuccess(res, tickets);
  }));

  app.get('/api/support-tickets/:id', asyncHandler(async (req: any, res: any) => {
    const ticket = await storage.getSupportTicket(parseInt(req.params.id));
    if (!ticket) return sendError(res, 'Support ticket not found', 404);
    sendSuccess(res, ticket);
  }));

  app.post('/api/support-tickets', validateSchema(insertSupportTicketSchema), asyncHandler(async (req: any, res: any) => {
    const ticket = await storage.createSupportTicket(req.validatedData);
    sendSuccess(res, ticket, 'Support ticket created successfully');
  }));

  app.put('/api/support-tickets/:id', validateSchema(updateSupportTicketSchema), asyncHandler(async (req: any, res: any) => {
    const ticket = await storage.updateSupportTicket(parseInt(req.params.id), req.validatedData);
    if (!ticket) return sendError(res, 'Support ticket not found', 404);
    sendSuccess(res, ticket, 'Support ticket updated successfully');
  }));

  app.delete('/api/support-tickets/:id', asyncHandler(async (req: any, res: any) => {
    const deleted = await storage.deleteSupportTicket(parseInt(req.params.id));
    if (!deleted) return sendError(res, 'Support ticket not found', 404);
    sendSuccess(res, null, 'Support ticket deleted successfully');
  }));

  // Analytics Routes
  app.get('/api/analytics/:metric/:period', asyncHandler(async (req: any, res: any) => {
    const { metric, period } = req.params;
    const analytics = await storage.getAnalytics(metric, period);
    sendSuccess(res, analytics);
  }));

  app.post('/api/analytics', asyncHandler(async (req: any, res: any) => {
    const { metric, value, period, metadata } = req.body;
    if (!metric || !value || !period) {
      return sendError(res, 'Missing required fields: metric, value, period');
    }
    await storage.recordAnalytic(metric, parseFloat(value), period, metadata);
    sendSuccess(res, null, 'Analytics recorded successfully');
  }));

  // Dashboard aggregation endpoints
  app.get('/api/dashboard/stats', asyncHandler(async (req: any, res: any) => {
    const customers = await storage.getCustomers();
    const products = await storage.getProducts();
    const orders = await storage.getOrders();
    const tickets = await storage.getSupportTickets();

    const stats = {
      totalCustomers: customers.length,
      totalProducts: products.length,
      totalOrders: orders.length,
      totalRevenue: orders.reduce((sum, order) => sum + parseFloat(order.total), 0),
      openTickets: tickets.filter(t => t.status === 'open').length,
      customersByTier: customers.reduce((acc, customer) => {
        acc[customer.tier] = (acc[customer.tier] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      ordersByStatus: orders.reduce((acc, order) => {
        acc[order.status] = (acc[order.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
    };

    sendSuccess(res, stats);
  }));

  app.get('/api/dashboard/recent-activity', asyncHandler(async (req: any, res: any) => {
    const orders = await storage.getOrders();
    const customers = await storage.getCustomers();
    const tickets = await storage.getSupportTickets();

    const recentOrders = orders
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5);

    const recentCustomers = customers
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5);

    const recentTickets = tickets
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5);

    const activity = {
      recentOrders,
      recentCustomers,
      recentTickets,
    };

    sendSuccess(res, activity);
  }));

  const httpServer = createServer(app);
  return httpServer;
}
