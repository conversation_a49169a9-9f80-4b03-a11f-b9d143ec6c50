import { Layout } from "@/components/Layout";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { 
  MessageSquare,
  Search,
  Plus,
  HelpCircle,
  Book,
  Video,
  Mail,
  Phone,
  Clock,
  CheckCircle,
  AlertCircle,
  User,
  Filter,
  Send,
  FileText,
  ExternalLink,
  Star,
  ThumbsUp,
  MessageCircle
} from "lucide-react";

const tickets = [
  {
    id: "#TK-001",
    subject: "Payment processing issue",
    status: "open",
    priority: "high",
    customer: "<PERSON> Johnson",
    category: "Billing",
    created: "2024-12-15T10:30:00Z",
    lastUpdate: "2024-12-15T14:20:00Z",
    assignee: "<PERSON>"
  },
  {
    id: "#TK-002",
    subject: "Website builder not loading",
    status: "in_progress",
    priority: "medium",
    customer: "<PERSON>",
    category: "Technical",
    created: "2024-12-14T16:45:00Z",
    lastUpdate: "2024-12-15T09:15:00Z",
    assignee: "Mike Chen"
  },
  {
    id: "#TK-003",
    subject: "How to add custom domain?",
    status: "resolved",
    priority: "low",
    customer: "Carol Davis",
    category: "General",
    created: "2024-12-13T11:20:00Z",
    lastUpdate: "2024-12-14T13:45:00Z",
    assignee: "Alex Rodriguez"
  }
];

const faqItems = [
  {
    id: 1,
    question: "How do I change my subscription plan?",
    answer: "You can upgrade or downgrade your plan in the billing section of your settings.",
    category: "Billing",
    views: 1247,
    helpful: 89,
    lastUpdated: "2024-12-10"
  },
  {
    id: 2,
    question: "Can I use my own domain name?",
    answer: "Yes! You can connect your custom domain in the site settings under the domains tab.",
    category: "Domains",
    views: 892,
    helpful: 76,
    lastUpdated: "2024-12-08"
  },
  {
    id: 3,
    question: "How to backup my website data?",
    answer: "You can export your website data from the export section in your dashboard.",
    category: "Data",
    views: 654,
    helpful: 82,
    lastUpdated: "2024-12-05"
  }
];

const articles = [
  {
    id: 1,
    title: "Getting Started with AppCraft Pro",
    description: "A comprehensive guide to setting up your first website",
    category: "Beginner",
    readTime: "10 min",
    views: 2341,
    rating: 4.8,
    lastUpdated: "2024-12-10"
  },
  {
    id: 2,
    title: "Advanced Website Customization",
    description: "Learn how to customize your site with CSS and custom code",
    category: "Advanced",
    readTime: "15 min",
    views: 1567,
    rating: 4.6,
    lastUpdated: "2024-12-08"
  },
  {
    id: 3,
    title: "SEO Best Practices",
    description: "Optimize your website for search engines",
    category: "Marketing",
    readTime: "8 min",
    views: 1890,
    rating: 4.9,
    lastUpdated: "2024-12-05"
  }
];

const SupportCenter = () => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "open":
        return "bg-destructive/10 text-destructive border-destructive/20";
      case "in_progress":
        return "bg-warning/10 text-warning border-warning/20";
      case "resolved":
        return "bg-success/10 text-success border-success/20";
      default:
        return "bg-muted text-muted-foreground";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-destructive/10 text-destructive border-destructive/20";
      case "medium":
        return "bg-warning/10 text-warning border-warning/20";
      case "low":
        return "bg-success/10 text-success border-success/20";
      default:
        return "bg-muted text-muted-foreground";
    }
  };

  return (
    <Layout>
      <div className="p-8 bg-muted/20">
        <div className="space-y-8">
              {/* Header */}
              <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
                <div>
                  <h1 className="text-3xl font-bold tracking-tight">Support Center</h1>
                  <p className="text-muted-foreground mt-1">
                    Get help, manage tickets, and access documentation
                  </p>
                </div>
                <div className="flex gap-3">
                  <Button variant="outline" size="sm">
                    <MessageSquare className="mr-2 h-4 w-4" />
                    Live Chat
                  </Button>
                  <Button size="sm">
                    <Plus className="mr-2 h-4 w-4" />
                    New Ticket
                  </Button>
                </div>
              </div>

              {/* Support Stats */}
              <div className="grid gap-6 md:grid-cols-4">
                <Card className="shadow-card border-0">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Open Tickets</p>
                        <p className="text-2xl font-bold">12</p>
                        <p className="text-xs text-warning flex items-center gap-1 mt-1">
                          <Clock className="h-3 w-3" />
                          3 need attention
                        </p>
                      </div>
                      <div className="p-3 rounded-xl bg-warning/10">
                        <MessageSquare className="h-5 w-5 text-warning" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="shadow-card border-0">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Avg. Response Time</p>
                        <p className="text-2xl font-bold">2.4h</p>
                        <p className="text-xs text-success flex items-center gap-1 mt-1">
                          <CheckCircle className="h-3 w-3" />
                          Under SLA
                        </p>
                      </div>
                      <div className="p-3 rounded-xl bg-success/10">
                        <Clock className="h-5 w-5 text-success" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="shadow-card border-0">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Satisfaction Rate</p>
                        <p className="text-2xl font-bold">4.8/5</p>
                        <div className="flex items-center gap-1 mt-1">
                           {[...Array(5)].map((_, i) => (
                             <Star key={i} className="h-3 w-3 text-warning fill-current" />
                           ))}
                         </div>
                       </div>
                       <div className="p-3 rounded-xl bg-warning/10">
                         <Star className="h-5 w-5 text-warning" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="shadow-card border-0">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Resolution Rate</p>
                        <p className="text-2xl font-bold">94%</p>
                        <p className="text-xs text-success flex items-center gap-1 mt-1">
                          <CheckCircle className="h-3 w-3" />
                          Within 24h
                        </p>
                      </div>
                      <div className="p-3 rounded-xl bg-success/10">
                        <CheckCircle className="h-5 w-5 text-success" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Tabs defaultValue="tickets" className="space-y-6">
                <TabsList>
                  <TabsTrigger value="tickets">Support Tickets</TabsTrigger>
                  <TabsTrigger value="faq">FAQ</TabsTrigger>
                  <TabsTrigger value="docs">Documentation</TabsTrigger>
                  <TabsTrigger value="contact">Contact</TabsTrigger>
                </TabsList>

                {/* Support Tickets Tab */}
                <TabsContent value="tickets" className="space-y-6">
                  <Card className="shadow-card border-0">
                    <CardHeader>
                      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
                        <CardTitle>Support Tickets</CardTitle>
                        <div className="flex gap-3">
                          <div className="relative">
                            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                            <Input placeholder="Search tickets..." className="w-64 pl-10" />
                          </div>
                          <Button variant="outline" size="sm">
                            <Filter className="mr-2 h-4 w-4" />
                            Filter
                          </Button>
                          <Button size="sm">
                            <Plus className="mr-2 h-4 w-4" />
                            New Ticket
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {tickets.map((ticket) => (
                          <div key={ticket.id} className="p-6 rounded-xl bg-muted/30 hover:bg-muted/50 transition-colors">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-center gap-3 mb-2">
                                  <h3 className="font-semibold text-lg">{ticket.subject}</h3>
                                  <Badge variant="outline" className={getStatusColor(ticket.status)}>
                                    {ticket.status.replace('_', ' ')}
                                  </Badge>
                                  <Badge variant="outline" className={getPriorityColor(ticket.priority)}>
                                    {ticket.priority} priority
                                  </Badge>
                                  <Badge variant="outline" className="bg-primary/10 text-primary">
                                    {ticket.category}
                                  </Badge>
                                </div>
                                
                                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-muted-foreground">
                                  <div>
                                    <p className="font-medium text-foreground">Ticket ID</p>
                                    <p>{ticket.id}</p>
                                  </div>
                                  <div>
                                    <p className="font-medium text-foreground">Customer</p>
                                    <p>{ticket.customer}</p>
                                  </div>
                                  <div>
                                    <p className="font-medium text-foreground">Assigned to</p>
                                    <p>{ticket.assignee}</p>
                                  </div>
                                  <div>
                                    <p className="font-medium text-foreground">Created</p>
                                    <p>{new Date(ticket.created).toLocaleDateString()}</p>
                                  </div>
                                </div>
                              </div>
                              
                              <div className="flex gap-2 ml-4">
                                <Button variant="ghost" size="sm">
                                  <MessageCircle className="h-4 w-4" />
                                </Button>
                                <Button variant="ghost" size="sm">
                                  <User className="h-4 w-4" />
                                </Button>
                                <Button size="sm">
                                  <Send className="mr-2 h-4 w-4" />
                                  Reply
                                </Button>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* FAQ Tab */}
                <TabsContent value="faq" className="space-y-6">
                  <Card className="shadow-card border-0">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle>Frequently Asked Questions</CardTitle>
                        <div className="relative">
                          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                          <Input placeholder="Search FAQ..." className="w-64 pl-10" />
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {faqItems.map((item) => (
                          <div key={item.id} className="p-6 rounded-xl bg-muted/30 hover:bg-muted/50 transition-colors">
                            <div className="flex items-start justify-between mb-3">
                              <div className="flex-1">
                                <div className="flex items-center gap-3 mb-2">
                                  <HelpCircle className="h-5 w-5 text-primary" />
                                  <h3 className="font-semibold text-lg">{item.question}</h3>
                                  <Badge variant="outline" className="bg-primary/10 text-primary">
                                    {item.category}
                                  </Badge>
                                </div>
                                <p className="text-muted-foreground mb-3">{item.answer}</p>
                                <div className="flex items-center gap-6 text-sm text-muted-foreground">
                                  <div className="flex items-center gap-1">
                                    <ThumbsUp className="h-3 w-3" />
                                    <span>{item.helpful}% helpful</span>
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <span>{item.views} views</span>
                                  </div>
                                  <div>
                                    Updated {new Date(item.lastUpdated).toLocaleDateString()}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Documentation Tab */}
                <TabsContent value="docs" className="space-y-6">
                  <Card className="shadow-card border-0">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle>Documentation & Guides</CardTitle>
                        <div className="relative">
                          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                          <Input placeholder="Search docs..." className="w-64 pl-10" />
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid gap-4 md:grid-cols-2">
                        {articles.map((article) => (
                          <div key={article.id} className="p-6 rounded-xl bg-muted/30 hover:bg-muted/50 transition-colors cursor-pointer">
                            <div className="flex items-start gap-4">
                              <div className="p-3 rounded-lg bg-primary/10">
                                <Book className="h-6 w-6 text-primary" />
                              </div>
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-2">
                                  <h3 className="font-semibold">{article.title}</h3>
                                  <ExternalLink className="h-4 w-4 text-muted-foreground" />
                                </div>
                                <p className="text-sm text-muted-foreground mb-3">{article.description}</p>
                                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                  <Badge variant="outline" className="bg-primary/10 text-primary">
                                    {article.category}
                                  </Badge>
                                  <span>{article.readTime}</span>
                                   <div className="flex items-center gap-1">
                                     <Star className="h-3 w-3 text-warning fill-current" />
                                     <span>{article.rating}</span>
                                   </div>
                                  <span>{article.views} views</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Contact Tab */}
                <TabsContent value="contact" className="space-y-6">
                  <div className="grid gap-6 md:grid-cols-3">
                    <Card className="shadow-card border-0 text-center">
                      <CardContent className="p-6">
                        <div className="p-3 rounded-xl bg-primary/10 w-fit mx-auto mb-4">
                          <MessageSquare className="h-6 w-6 text-primary" />
                        </div>
                        <h3 className="font-semibold mb-2">Live Chat</h3>
                        <p className="text-sm text-muted-foreground mb-4">Get instant help from our support team</p>
                        <Button className="w-full">Start Chat</Button>
                      </CardContent>
                    </Card>

                    <Card className="shadow-card border-0 text-center">
                      <CardContent className="p-6">
                        <div className="p-3 rounded-xl bg-success/10 w-fit mx-auto mb-4">
                          <Mail className="h-6 w-6 text-success" />
                        </div>
                        <h3 className="font-semibold mb-2">Email Support</h3>
                        <p className="text-sm text-muted-foreground mb-4">Send us a detailed message</p>
                        <Button variant="outline" className="w-full">Send Email</Button>
                      </CardContent>
                    </Card>

                    <Card className="shadow-card border-0 text-center">
                      <CardContent className="p-6">
                        <div className="p-3 rounded-xl bg-warning/10 w-fit mx-auto mb-4">
                          <Phone className="h-6 w-6 text-warning" />
                        </div>
                        <h3 className="font-semibold mb-2">Phone Support</h3>
                        <p className="text-sm text-muted-foreground mb-4">Call us for urgent issues</p>
                        <Button variant="outline" className="w-full">Call Now</Button>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>
              </Tabs>
        </div>
      </div>
    </Layout>
  );
};

export default SupportCenter;