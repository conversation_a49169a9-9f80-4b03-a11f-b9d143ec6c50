import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { StatsCard } from "./StatsCard";
import { ActivityFeed } from "./ActivityFeed";
import { MetricCard } from "./MetricCard";
import { 
  Users, 
  ShoppingCart, 
  Package, 
  DollarSign, 
  TrendingUp, 
  AlertCircle,
  Clock,
  CheckCircle,
  ArrowUpRight,
  BarChart3,
  PieChart,
  Activity
} from "lucide-react";

export function EnhancedDashboard() {
  const { data: stats, isLoading: statsLoading } = useQuery({
    queryKey: ['/api/dashboard/stats'],
    queryFn: async () => {
      const response = await fetch('/api/dashboard/stats');
      return response.json();
    },
  });

  const { data: recentActivity, isLoading: activityLoading } = useQuery({
    queryKey: ['/api/dashboard/recent-activity'],
    queryFn: async () => {
      const response = await fetch('/api/dashboard/recent-activity');
      return response.json();
    },
  });

  const { data: customers } = useQuery({
    queryKey: ['/api/customers'],
    queryFn: async () => {
      const response = await fetch('/api/customers');
      return response.json();
    },
  });

  const { data: products } = useQuery({
    queryKey: ['/api/products'],
    queryFn: async () => {
      const response = await fetch('/api/products');
      return response.json();
    },
  });

  const { data: orders } = useQuery({
    queryKey: ['/api/orders'],
    queryFn: async () => {
      const response = await fetch('/api/orders');
      return response.json();
    },
  });

  if (statsLoading || activityLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-16 bg-muted rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  const dashboardStats = stats?.data || {};
  const activity = recentActivity?.data || {};

  return (
    <div className="space-y-6">
      {/* Key Performance Indicators */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Revenue"
          value={`$${dashboardStats.totalRevenue?.toLocaleString() || '0'}`}
          change="+12.5%"
          changeType="positive"
          icon={<DollarSign className="w-5 h-5" />}
          description="vs last month"
        />
        <StatsCard
          title="Total Orders"
          value={dashboardStats.totalOrders?.toString() || '0'}
          change="+8.2%"
          changeType="positive"
          icon={<ShoppingCart className="w-5 h-5" />}
          description="vs last month"
        />
        <StatsCard
          title="Total Customers"
          value={dashboardStats.totalCustomers?.toString() || '0'}
          change="+15.3%"
          changeType="positive"
          icon={<Users className="w-5 h-5" />}
          description="vs last month"
        />
        <StatsCard
          title="Open Tickets"
          value={dashboardStats.openTickets?.toString() || '0'}
          change="-2.1%"
          changeType="negative"
          icon={<AlertCircle className="w-5 h-5" />}
          description="vs last month"
        />
      </div>

      {/* Dashboard Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="customers">Customers</TabsTrigger>
          <TabsTrigger value="products">Products</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Recent Activity Feed */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="w-5 h-5" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ActivityFeed />
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button className="w-full justify-start" variant="outline">
                    <Package className="w-4 h-4 mr-2" />
                    Add New Product
                  </Button>
                  <Button className="w-full justify-start" variant="outline">
                    <Users className="w-4 h-4 mr-2" />
                    View Customers
                  </Button>
                  <Button className="w-full justify-start" variant="outline">
                    <ShoppingCart className="w-4 h-4 mr-2" />
                    Process Orders
                  </Button>
                  <Button className="w-full justify-start" variant="outline">
                    <BarChart3 className="w-4 h-4 mr-2" />
                    View Analytics
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">System Status</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">API Status</span>
                    <Badge className="bg-green-100 text-green-800">
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Operational
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Database</span>
                    <Badge className="bg-green-100 text-green-800">
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Connected
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Last Backup</span>
                    <Badge variant="outline">
                      <Clock className="w-3 h-3 mr-1" />
                      2 hours ago
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <MetricCard
              title="Revenue Analytics"
              subtitle="Monthly performance overview"
              action={{
                label: "View Details",
                icon: <ArrowUpRight className="w-4 h-4" />,
                onClick: () => console.log("View revenue details")
              }}
            >
              <div className="space-y-4">
                <div className="text-3xl font-bold text-foreground">
                  ${dashboardStats.totalRevenue?.toLocaleString() || '0'}
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">This month</span>
                    <span className="font-medium">$12,345</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Last month</span>
                    <span className="font-medium">$10,987</span>
                  </div>
                </div>
              </div>
            </MetricCard>

            <MetricCard
              title="Customer Tiers"
              subtitle="Customer distribution by tier"
              action={{
                label: "Manage Tiers",
                icon: <PieChart className="w-4 h-4" />,
                onClick: () => console.log("Manage customer tiers")
              }}
            >
              <div className="space-y-3">
                {Object.entries(dashboardStats.customersByTier || {}).map(([tier, count]) => (
                  <div key={tier} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className={`w-3 h-3 rounded-full ${
                        tier === 'vip' ? 'bg-yellow-500' : 
                        tier === 'premium' ? 'bg-purple-500' : 
                        'bg-blue-500'
                      }`} />
                      <span className="text-sm font-medium capitalize">{tier}</span>
                    </div>
                    <span className="text-sm text-muted-foreground">{count as number}</span>
                  </div>
                ))}
              </div>
            </MetricCard>
          </div>
        </TabsContent>

        <TabsContent value="customers" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Customer Management</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-muted/20 rounded-lg">
                  <div className="text-2xl font-bold text-foreground">
                    {customers?.data?.length || 0}
                  </div>
                  <div className="text-sm text-muted-foreground">Total Customers</div>
                </div>
                <div className="text-center p-4 bg-muted/20 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {customers?.data?.filter((c: any) => c.status === 'active').length || 0}
                  </div>
                  <div className="text-sm text-muted-foreground">Active Customers</div>
                </div>
                <div className="text-center p-4 bg-muted/20 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600">
                    {customers?.data?.filter((c: any) => c.tier === 'vip').length || 0}
                  </div>
                  <div className="text-sm text-muted-foreground">VIP Customers</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="products" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Product Management</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-muted/20 rounded-lg">
                  <div className="text-2xl font-bold text-foreground">
                    {products?.data?.length || 0}
                  </div>
                  <div className="text-sm text-muted-foreground">Total Products</div>
                </div>
                <div className="text-center p-4 bg-muted/20 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {products?.data?.filter((p: any) => p.status === 'active').length || 0}
                  </div>
                  <div className="text-sm text-muted-foreground">Active Products</div>
                </div>
                <div className="text-center p-4 bg-muted/20 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {products?.data?.reduce((sum: number, p: any) => sum + (p.stock || 0), 0) || 0}
                  </div>
                  <div className="text-sm text-muted-foreground">Total Stock</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}