import { useState } from "react";
import { Building2, User, Mail, Key, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";

interface CreateAccountModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAccountCreated: (account: any) => void;
}

export function CreateAccountModal({ isOpen, onClose, onAccountCreated }: CreateAccountModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    type: 'personal' as 'personal' | 'business',
    password: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newAccount = {
        id: Date.now().toString(),
        name: formData.name,
        email: formData.email,
        type: formData.type,
        isActive: false,
      };

      onAccountCreated(newAccount);
      
      toast({
        title: "Account Created",
        description: `Successfully created ${formData.type} account for ${formData.name}`,
      });

      // Reset form
      setFormData({
        name: '',
        email: '',
        type: 'personal',
        password: '',
      });
      
      onClose();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create account. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="glass-card border-primary/20 max-w-md">
        <DialogHeader>
          <DialogTitle className="text-foreground text-lg font-semibold">
            Create New Account
          </DialogTitle>
          <DialogDescription className="text-foreground/60">
            Add a new personal or business account to your workspace.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="account-type" className="text-foreground/80 text-sm font-medium">
                Account Type
              </Label>
              <RadioGroup
                value={formData.type}
                onValueChange={(value) => handleInputChange('type', value)}
                className="mt-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="personal" id="personal" />
                  <Label htmlFor="personal" className="flex items-center gap-2 cursor-pointer">
                    <User className="w-4 h-4" />
                    Personal Account
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="business" id="business" />
                  <Label htmlFor="business" className="flex items-center gap-2 cursor-pointer">
                    <Building2 className="w-4 h-4" />
                    Business Account
                  </Label>
                </div>
              </RadioGroup>
            </div>

            <div>
              <Label htmlFor="account-name" className="text-foreground/80 text-sm font-medium">
                Account Name
              </Label>
              <Input
                id="account-name"
                type="text"
                placeholder={formData.type === 'business' ? 'Company Name' : 'Your Name'}
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className="glass-input mt-1"
                required
              />
            </div>

            <div>
              <Label htmlFor="account-email" className="text-foreground/80 text-sm font-medium">
                Email Address
              </Label>
              <Input
                id="account-email"
                type="email"
                placeholder="Enter email address"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className="glass-input mt-1"
                required
              />
            </div>

            <div>
              <Label htmlFor="account-password" className="text-foreground/80 text-sm font-medium">
                Password
              </Label>
              <Input
                id="account-password"
                type="password"
                placeholder="Create a secure password"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                className="glass-input mt-1"
                required
              />
            </div>
          </div>

          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1 glass-button"
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex-1 glass-button bg-primary/20 border-primary/30 text-primary hover:bg-primary/30"
              disabled={isLoading}
            >
              {isLoading ? 'Creating...' : 'Create Account'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}