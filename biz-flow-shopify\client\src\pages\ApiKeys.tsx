import { useState } from "react";
import { Layout } from "@/components/Layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { 
  Key, 
  Shield, 
  Eye, 
  EyeOff, 
  Copy, 
  Plus, 
  Trash2, 
  Edit, 
  Activity,
  Lock,
  AlertTriangle,
  Settings,
  RotateCcw,
  Calendar,
  TrendingUp
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const ApiKeys = () => {
  const { toast } = useToast();
  const [showKeys, setShowKeys] = useState<{[key: string]: boolean}>({});
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  const apiKeys = [
    {
      id: "ak_prod_1234567890",
      name: "Production API Key",
      description: "Main production key for app integrations",
      permissions: ["read", "write", "admin"],
      environment: "production",
      created: "2024-01-15",
      lastUsed: "2 hours ago",
      usageCount: 15420,
      status: "active",
      expiresAt: "2024-12-31"
    },
    {
      id: "ak_test_0987654321",
      name: "Development Testing",
      description: "Testing key for development environment",
      permissions: ["read", "write"],
      environment: "development",
      created: "2024-01-20",
      lastUsed: "5 minutes ago",
      usageCount: 892,
      status: "active",
      expiresAt: "2024-06-30"
    },
    {
      id: "ak_staging_5555555555",
      name: "Staging Environment",
      description: "Key for staging deployment and testing",
      permissions: ["read"],
      environment: "staging",
      created: "2024-01-10",
      lastUsed: "Never",
      usageCount: 0,
      status: "inactive",
      expiresAt: "2024-03-31"
    }
  ];

  const securityLogs = [
    { time: "2 hours ago", action: "API Key Used", key: "ak_prod_1234567890", ip: "*************", location: "San Francisco, CA" },
    { time: "5 minutes ago", action: "API Key Used", key: "ak_test_0987654321", ip: "********", location: "Development Server" },
    { time: "1 day ago", action: "API Key Created", key: "ak_test_0987654321", ip: "************", location: "San Francisco, CA" },
    { time: "3 days ago", action: "API Key Regenerated", key: "ak_prod_1234567890", ip: "*************", location: "San Francisco, CA" }
  ];

  const toggleKeyVisibility = (keyId: string) => {
    setShowKeys(prev => ({
      ...prev,
      [keyId]: !prev[keyId]
    }));
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied!",
      description: "API key copied to clipboard",
    });
  };

  const maskKey = (key: string) => {
    if (showKeys[key]) return key;
    return key.substring(0, 8) + "..." + key.substring(key.length - 4);
  };

  const getEnvironmentColor = (env: string) => {
    switch (env) {
      case "production": return "bg-destructive/10 text-destructive border-destructive/20";
      case "staging": return "bg-warning/10 text-warning border-warning/20";
      case "development": return "bg-info/10 text-info border-info/20";
      default: return "bg-muted text-muted-foreground border-border";
    }
  };

  const getStatusColor = (status: string) => {
    return status === "active" ? 
      "bg-success/10 text-success border-success/20" : 
      "bg-muted text-muted-foreground border-border";
  };

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-subtle">
        <div className="p-6 lg:p-8 max-w-7xl mx-auto space-y-8">
          {/* Header Section */}
          <div className="space-y-2">
            <h1 className="text-3xl font-display font-bold tracking-tight bg-gradient-primary bg-clip-text text-transparent">
              API Keys & Security
            </h1>
            <p className="text-base text-muted-foreground max-w-2xl">
              Manage your API keys, monitor usage, and secure your integrations
            </p>
          </div>

          {/* Security Overview */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card className="glass-card border-border/50 hover:border-primary/20 transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-muted-foreground">Active Keys</p>
                    <p className="text-2xl font-bold">2</p>
                    <p className="text-xs text-success font-medium">All secure</p>
                  </div>
                  <div className="w-12 h-12 rounded-xl bg-primary/10 flex items-center justify-center">
                    <Key className="w-6 h-6 text-primary" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="glass-card border-border/50 hover:border-primary/20 transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-muted-foreground">Total Requests</p>
                    <p className="text-2xl font-bold">16.3K</p>
                    <p className="text-xs text-success font-medium flex items-center gap-1">
                      <TrendingUp className="w-3 h-3" />
                      +1.2K today
                    </p>
                  </div>
                  <div className="w-12 h-12 rounded-xl bg-success/10 flex items-center justify-center">
                    <Activity className="w-6 h-6 text-success" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="glass-card border-border/50 hover:border-primary/20 transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-muted-foreground">Security Score</p>
                    <p className="text-2xl font-bold">98%</p>
                    <p className="text-xs text-success font-medium">Excellent</p>
                  </div>
                  <div className="w-12 h-12 rounded-xl bg-success/10 flex items-center justify-center">
                    <Shield className="w-6 h-6 text-success" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="glass-card border-border/50 hover:border-warning/20 transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-muted-foreground">Expiring Soon</p>
                    <p className="text-2xl font-bold">1</p>
                    <p className="text-xs text-warning font-medium">In 2 months</p>
                  </div>
                  <div className="w-12 h-12 rounded-xl bg-warning/10 flex items-center justify-center">
                    <AlertTriangle className="w-6 h-6 text-warning" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* API Keys Management */}
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold text-foreground">API Keys</h2>
              <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-elegant">
                    <Plus className="w-4 h-4 mr-2" />
                    Create API Key
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[500px] glass-card border-border/50">
                  <DialogHeader>
                    <DialogTitle className="text-xl font-semibold">Create New API Key</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4 py-4">
                    <div className="space-y-2">
                      <Label htmlFor="name" className="text-sm font-medium">Key Name</Label>
                      <Input 
                        id="name" 
                        placeholder="e.g., Mobile App Integration"
                        className="bg-background/50 border-border/50 focus:border-primary/50"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="description" className="text-sm font-medium">Description</Label>
                      <Textarea 
                        id="description" 
                        placeholder="Describe what this key will be used for..."
                        className="bg-background/50 border-border/50 focus:border-primary/50"
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="environment" className="text-sm font-medium">Environment</Label>
                        <Select>
                          <SelectTrigger className="bg-background/50 border-border/50">
                            <SelectValue placeholder="Select environment" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="production">Production</SelectItem>
                            <SelectItem value="staging">Staging</SelectItem>
                            <SelectItem value="development">Development</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="permissions" className="text-sm font-medium">Permissions</Label>
                        <Select>
                          <SelectTrigger className="bg-background/50 border-border/50">
                            <SelectValue placeholder="Select permissions" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="read">Read Only</SelectItem>
                            <SelectItem value="read-write">Read & Write</SelectItem>
                            <SelectItem value="admin">Admin Access</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="expiry" className="text-sm font-medium">Expiration Date</Label>
                      <Input 
                        id="expiry" 
                        type="date"
                        className="bg-background/50 border-border/50 focus:border-primary/50"
                      />
                    </div>
                    <div className="flex justify-end gap-3 pt-4">
                      <Button 
                        variant="outline" 
                        onClick={() => setIsCreateDialogOpen(false)}
                        className="border-border/50"
                      >
                        Cancel
                      </Button>
                      <Button 
                        onClick={() => setIsCreateDialogOpen(false)}
                        className="bg-primary hover:bg-primary/90 text-primary-foreground"
                      >
                        Create Key
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>

            <div className="space-y-4">
              {apiKeys.map((key) => (
                <Card 
                  key={key.id} 
                  className="glass-card border-border/50 hover:border-primary/20 hover:shadow-elegant transition-all duration-300"
                >
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 space-y-4">
                        {/* Header */}
                        <div className="flex items-center gap-3">
                          <h3 className="text-lg font-semibold text-foreground">{key.name}</h3>
                          <Badge className={getEnvironmentColor(key.environment)} variant="outline">
                            {key.environment}
                          </Badge>
                          <Badge className={getStatusColor(key.status)} variant="outline">
                            {key.status}
                          </Badge>
                        </div>
                        
                        <p className="text-sm text-muted-foreground">{key.description}</p>
                        
                        {/* API Key Display */}
                        <div className="bg-muted/30 rounded-lg p-3 border border-border/50">
                          <div className="flex items-center gap-3">
                            <Lock className="w-4 h-4 text-muted-foreground flex-shrink-0" />
                            <code className="text-sm font-mono flex-1 text-foreground">
                              {maskKey(key.id)}
                            </code>
                            <div className="flex gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => toggleKeyVisibility(key.id)}
                                className="h-8 w-8 p-0 hover:bg-background/50"
                              >
                                {showKeys[key.id] ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => copyToClipboard(key.id)}
                                className="h-8 w-8 p-0 hover:bg-background/50"
                              >
                                <Copy className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                        </div>

                        {/* Metadata Grid */}
                        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                          <div className="space-y-1">
                            <p className="text-xs text-muted-foreground font-medium">Created</p>
                            <p className="text-sm font-medium">{key.created}</p>
                          </div>
                          <div className="space-y-1">
                            <p className="text-xs text-muted-foreground font-medium">Last Used</p>
                            <p className="text-sm font-medium">{key.lastUsed}</p>
                          </div>
                          <div className="space-y-1">
                            <p className="text-xs text-muted-foreground font-medium">Usage Count</p>
                            <p className="text-sm font-medium">{key.usageCount.toLocaleString()}</p>
                          </div>
                          <div className="space-y-1">
                            <p className="text-xs text-muted-foreground font-medium">Expires</p>
                            <p className="text-sm font-medium">{key.expiresAt}</p>
                          </div>
                        </div>

                        {/* Permissions */}
                        <div className="flex items-center gap-2 flex-wrap">
                          <span className="text-xs text-muted-foreground font-medium">Permissions:</span>
                          {key.permissions.map((permission) => (
                            <Badge 
                              key={permission} 
                              variant="secondary" 
                              className="text-xs bg-primary/10 text-primary border-primary/20"
                            >
                              {permission}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex gap-1 ml-4">
                        <Button 
                          variant="ghost" 
                          size="sm"
                          className="h-8 w-8 p-0 hover:bg-background/50"
                        >
                          <Settings className="w-4 h-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          className="h-8 w-8 p-0 hover:bg-background/50"
                        >
                          <RotateCcw className="w-4 h-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          className="h-8 w-8 p-0 hover:bg-background/50"
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          className="h-8 w-8 p-0 hover:bg-destructive/10 hover:text-destructive"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Security Logs */}
            <Card className="glass-card border-border/50">
              <CardHeader>
                <CardTitle className="flex items-center gap-3 text-lg font-semibold">
                  <div className="w-8 h-8 rounded-lg bg-primary/10 flex items-center justify-center">
                    <Shield className="w-4 h-4 text-primary" />
                  </div>
                  Security Activity Log
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {securityLogs.map((log, index) => (
                    <div 
                      key={index} 
                      className="flex items-center justify-between p-4 rounded-lg bg-muted/30 border border-border/50 hover:bg-muted/50 transition-colors"
                    >
                      <div className="flex items-center gap-4">
                        <div className="w-2 h-2 bg-primary rounded-full flex-shrink-0" />
                        <div className="space-y-1">
                          <div className="font-medium text-sm text-foreground">{log.action}</div>
                          <div className="text-xs text-muted-foreground">
                            <code className="bg-background/50 px-1.5 py-0.5 rounded text-xs">
                              {log.key}
                            </code>
                            <span className="mx-2">•</span>
                            <span>{log.ip}</span>
                            <span className="mx-1">({log.location})</span>
                          </div>
                        </div>
                      </div>
                      <div className="text-xs text-muted-foreground flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        {log.time}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default ApiKeys;