import { Layout } from "@/components/Layout";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Code, 
  GitBranch, 
  Bug, 
  Rocket, 
  Clock, 
  Users, 
  AlertTriangle, 
  CheckCircle, 
  Circle,
  Play,
  Pause,
  Settings,
  Plus,
  Filter,
  Search,
  Calendar,
  Target,
  Activity,
  Database,
  Shield,
  Zap,
  FileCode,
  Terminal,
  Eye,
  Edit,
  TrendingUp,
  GitPullRequest,
  Server,
  Monitor
} from "lucide-react";

const Development = () => {
  const sprints = [
    {
      id: 1,
      name: "User Authentication System",
      status: "in-progress",
      progress: 75,
      startDate: "2024-01-15",
      endDate: "2024-01-29",
      tasks: 12,
      completed: 9,
      assignees: ["<PERSON>", "<PERSON>"],
      priority: "high"
    },
    {
      id: 2,
      name: "Payment Integration",
      status: "planned",
      progress: 0,
      startDate: "2024-01-30",
      endDate: "2024-02-13",
      tasks: 8,
      completed: 0,
      assignees: ["Mike Chen", "Lisa Rodriguez"],
      priority: "high"
    },
    {
      id: 3,
      name: "Mobile Responsive Design",
      status: "completed",
      progress: 100,
      startDate: "2024-01-01",
      endDate: "2024-01-14",
      tasks: 15,
      completed: 15,
      assignees: ["Alex Johnson"],
      priority: "medium"
    }
  ];

  const bugs = [
    {
      id: 1,
      title: "Login form validation not working on iOS Safari",
      severity: "high",
      status: "open",
      reporter: "QA Team",
      assignee: "John Doe",
      created: "2024-01-20",
      environment: "Production"
    },
    {
      id: 2,
      title: "Dashboard charts loading slowly with large datasets",
      severity: "medium",
      status: "in-progress",
      reporter: "Product Team",
      assignee: "Sarah Wilson",
      created: "2024-01-18",
      environment: "Staging"
    },
    {
      id: 3,
      title: "Email notifications not sending for premium users",
      severity: "critical",
      status: "open",
      reporter: "Customer Support",
      assignee: "Mike Chen",
      created: "2024-01-22",
      environment: "Production"
    }
  ];

  const deployments = [
    {
      id: 1,
      version: "v2.4.1",
      environment: "Production",
      status: "success",
      deployedAt: "2024-01-20 14:30",
      deployedBy: "CI/CD Pipeline",
      features: ["Bug fixes", "Performance improvements", "Security updates"]
    },
    {
      id: 2,
      version: "v2.5.0-beta",
      environment: "Staging",
      status: "success",
      deployedAt: "2024-01-22 09:15",
      deployedBy: "John Doe",
      features: ["New dashboard", "Enhanced analytics", "Mobile improvements"]
    },
    {
      id: 3,
      version: "v2.5.0",
      environment: "Production",
      status: "pending",
      deployedAt: "Scheduled for 2024-01-25 16:00",
      deployedBy: "CI/CD Pipeline",
      features: ["Major feature release", "UI overhaul", "API v3"]
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": case "success": return "bg-success-light text-success border-success/20";
      case "in-progress": return "bg-primary-light text-primary border-primary/20";
      case "planned": case "pending": return "bg-muted text-muted-foreground border-border";
      case "open": return "bg-destructive-light text-destructive border-destructive/20";
      default: return "bg-muted text-muted-foreground border-border";
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "critical": return "bg-destructive-light text-destructive border-destructive/20";
      case "high": return "bg-warning-light text-warning border-warning/20";
      case "medium": return "bg-info-light text-info border-info/20";
      case "low": return "bg-success-light text-success border-success/20";
      default: return "bg-muted text-muted-foreground border-border";
    }
  };

  return (
    <Layout>
      <div className="p-8 space-y-8">
        {/* Hero Section */}
        <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-primary/5 via-primary/10 to-primary/5 p-8 md:p-12">
          <div className="relative z-10">
            <div className="flex items-center gap-4 mb-6">
              <div className="p-3 bg-primary/20 rounded-2xl backdrop-blur-sm">
                <Code className="h-8 w-8 text-primary" />
              </div>
              <div>
                <h1 className="text-4xl font-bold tracking-tight">Development Hub</h1>
                <p className="text-xl text-muted-foreground mt-2">
                  Streamline your development workflow with modern tools and insights
                </p>
              </div>
            </div>
            
            {/* Quick Actions */}
            <div className="flex flex-wrap gap-3">
              <Button className="bg-primary/90 hover:bg-primary">
                <GitBranch className="h-4 w-4 mr-2" />
                Create Branch
              </Button>
              <Button variant="outline" className="border-primary/20 hover:bg-primary/5">
                <Rocket className="h-4 w-4 mr-2" />
                Deploy to Staging
              </Button>
              <Button variant="outline" className="border-primary/20 hover:bg-primary/5">
                <Bug className="h-4 w-4 mr-2" />
                Report Issue
              </Button>
            </div>
          </div>
          
          {/* Background decoration */}
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary/10 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-primary/5 rounded-full blur-3xl"></div>
        </div>

        {/* Modern Stats Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <Card className="glass-card p-6 hover:shadow-lg transition-all duration-300">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Active Sprints</p>
                <p className="text-3xl font-bold tracking-tight">3</p>
                <div className="flex items-center gap-1 text-xs text-success">
                  <TrendingUp className="h-3 w-3" />
                  <span>2 in progress</span>
                </div>
              </div>
              <div className="p-3 bg-primary/10 rounded-2xl">
                <Target className="h-6 w-6 text-primary" />
              </div>
            </div>
          </Card>

          <Card className="glass-card p-6 hover:shadow-lg transition-all duration-300">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Critical Issues</p>
                <p className="text-3xl font-bold tracking-tight">3</p>
                <div className="flex items-center gap-1 text-xs text-destructive">
                  <AlertTriangle className="h-3 w-3" />
                  <span>Needs attention</span>
                </div>
              </div>
              <div className="p-3 bg-destructive/10 rounded-2xl">
                <Bug className="h-6 w-6 text-destructive" />
              </div>
            </div>
          </Card>

          <Card className="glass-card p-6 hover:shadow-lg transition-all duration-300">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Deployments</p>
                <p className="text-3xl font-bold tracking-tight">24</p>
                <div className="flex items-center gap-1 text-xs text-success">
                  <CheckCircle className="h-3 w-3" />
                  <span>This month</span>
                </div>
              </div>
              <div className="p-3 bg-success/10 rounded-2xl">
                <Rocket className="h-6 w-6 text-success" />
              </div>
            </div>
          </Card>

          <Card className="glass-card p-6 hover:shadow-lg transition-all duration-300">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Test Coverage</p>
                <p className="text-3xl font-bold tracking-tight">94%</p>
                <div className="flex items-center gap-1 text-xs text-success">
                  <Shield className="h-3 w-3" />
                  <span>+7% this week</span>
                </div>
              </div>
              <div className="p-3 bg-info/10 rounded-2xl">
                <Code className="h-6 w-6 text-info" />
              </div>
            </div>
          </Card>
        </div>

        <Tabs defaultValue="sprints" className="space-y-8">
          <TabsList className="grid w-full lg:w-[900px] grid-cols-5 bg-muted/50 p-1 rounded-2xl">
            <TabsTrigger value="sprints" className="rounded-xl data-[state=active]:bg-card data-[state=active]:shadow-sm">
              <Target className="h-4 w-4 mr-2" />
              Sprints
            </TabsTrigger>
            <TabsTrigger value="bugs" className="rounded-xl data-[state=active]:bg-card data-[state=active]:shadow-sm">
              <Bug className="h-4 w-4 mr-2" />
              Issues
            </TabsTrigger>
            <TabsTrigger value="deployments" className="rounded-xl data-[state=active]:bg-card data-[state=active]:shadow-sm">
              <Rocket className="h-4 w-4 mr-2" />
              Deployments
            </TabsTrigger>
            <TabsTrigger value="repositories" className="rounded-xl data-[state=active]:bg-card data-[state=active]:shadow-sm">
              <GitBranch className="h-4 w-4 mr-2" />
              Repos
            </TabsTrigger>
            <TabsTrigger value="ci-cd" className="rounded-xl data-[state=active]:bg-card data-[state=active]:shadow-sm">
              <Server className="h-4 w-4 mr-2" />
              CI/CD
            </TabsTrigger>
          </TabsList>

          <TabsContent value="sprints" className="space-y-6">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
              <div>
                <h2 className="text-2xl font-semibold tracking-tight">Sprint Management</h2>
                <p className="text-muted-foreground">Track and manage development sprints</p>
              </div>
              <div className="flex gap-3">
                <Button variant="outline" className="hover:bg-muted/50">
                  <Filter className="h-4 w-4 mr-2" />
                  Filter
                </Button>
                <Button className="bg-primary hover:bg-primary/90">
                  <Plus className="h-4 w-4 mr-2" />
                  New Sprint
                </Button>
              </div>
            </div>

            <div className="grid gap-6">
              {sprints.map((sprint) => (
                <Card key={sprint.id} className="glass-card hover:shadow-lg transition-all duration-300 border-0">
                  <CardContent className="p-8">
                    <div className="flex items-start justify-between mb-6">
                      <div className="flex-1">
                        <div className="flex flex-wrap items-center gap-3 mb-4">
                          <h3 className="text-xl font-semibold tracking-tight">{sprint.name}</h3>
                          <Badge className={`${getStatusColor(sprint.status)} border px-3 py-1 rounded-full`}>
                            {sprint.status.replace('-', ' ')}
                          </Badge>
                          <Badge variant="outline" className={`px-3 py-1 rounded-full ${
                            sprint.priority === 'high' ? 'border-destructive/20 text-destructive bg-destructive/5' :
                            sprint.priority === 'medium' ? 'border-warning/20 text-warning bg-warning/5' :
                            'border-success/20 text-success bg-success/5'
                          }`}>
                            {sprint.priority} priority
                          </Badge>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 text-sm text-muted-foreground">
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4" />
                            <span>{sprint.startDate} - {sprint.endDate}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Users className="h-4 w-4" />
                            <span>{sprint.assignees.join(', ')}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Activity className="h-4 w-4" />
                            <span>{sprint.completed}/{sprint.tasks} tasks completed</span>
                          </div>
                        </div>
                        
                        <div className="space-y-3">
                          <div className="flex justify-between items-center text-sm">
                            <span className="font-medium">Sprint Progress</span>
                            <span className="text-muted-foreground">{sprint.progress}%</span>
                          </div>
                          <Progress value={sprint.progress} className="h-3 rounded-full" />
                        </div>
                      </div>
                      
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm" className="hover:bg-muted/50">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm" className="hover:bg-muted/50">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm" className="hover:bg-muted/50">
                          {sprint.status === 'in-progress' ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="bugs" className="space-y-6">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
              <div>
                <h2 className="text-2xl font-semibold tracking-tight">Issue Tracking</h2>
                <p className="text-muted-foreground">Monitor and resolve development issues</p>
              </div>
              <div className="flex gap-3">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input placeholder="Search issues..." className="pl-10 w-64 rounded-xl" />
                </div>
                <Button className="bg-primary hover:bg-primary/90">
                  <Plus className="h-4 w-4 mr-2" />
                  Report Issue
                </Button>
              </div>
            </div>

            <div className="grid gap-6">
              {bugs.map((bug) => (
                <Card key={bug.id} className="glass-card hover:shadow-lg transition-all duration-300 border-0">
                  <CardContent className="p-8">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex flex-wrap items-center gap-3 mb-4">
                          <h3 className="text-lg font-semibold tracking-tight">{bug.title}</h3>
                          <Badge className={`${getSeverityColor(bug.severity)} border px-3 py-1 rounded-full`}>
                            {bug.severity}
                          </Badge>
                          <Badge className={`${getStatusColor(bug.status)} border px-3 py-1 rounded-full`}>
                            {bug.status}
                          </Badge>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                          <div className="space-y-1">
                            <span className="font-medium text-muted-foreground">Reporter</span>
                            <p className="text-foreground">{bug.reporter}</p>
                          </div>
                          <div className="space-y-1">
                            <span className="font-medium text-muted-foreground">Assignee</span>
                            <p className="text-foreground">{bug.assignee}</p>
                          </div>
                          <div className="space-y-1">
                            <span className="font-medium text-muted-foreground">Created</span>
                            <p className="text-foreground">{bug.created}</p>
                          </div>
                          <div className="space-y-1">
                            <span className="font-medium text-muted-foreground">Environment</span>
                            <p className="text-foreground">{bug.environment}</p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm" className="hover:bg-muted/50">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm" className="hover:bg-muted/50">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="deployments" className="space-y-6">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
              <div>
                <h2 className="text-2xl font-semibold tracking-tight">Deployment Pipeline</h2>
                <p className="text-muted-foreground">Monitor and manage application deployments</p>
              </div>
              <Button className="bg-primary hover:bg-primary/90">
                <Rocket className="h-4 w-4 mr-2" />
                Deploy Now
              </Button>
            </div>

            <div className="grid gap-6">
              {deployments.map((deployment) => (
                <Card key={deployment.id} className="glass-card hover:shadow-lg transition-all duration-300 border-0">
                  <CardContent className="p-8">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex flex-wrap items-center gap-3 mb-4">
                          <h3 className="text-xl font-semibold tracking-tight">{deployment.version}</h3>
                          <Badge variant="outline" className="px-3 py-1 rounded-full border-border/50">
                            {deployment.environment}
                          </Badge>
                          <Badge className={`${getStatusColor(deployment.status)} border px-3 py-1 rounded-full`}>
                            {deployment.status}
                          </Badge>
                        </div>
                        
                        <div className="flex items-center gap-4 mb-6 text-sm text-muted-foreground">
                          <div className="flex items-center gap-2">
                            <Users className="h-4 w-4" />
                            <span>Deployed by {deployment.deployedBy}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4" />
                            <span>{deployment.deployedAt}</span>
                          </div>
                        </div>
                        
                        <div className="space-y-3">
                          <p className="text-sm font-medium">Release Features:</p>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                            {deployment.features.map((feature, index) => (
                              <div key={index} className="flex items-center gap-3 p-3 bg-muted/30 rounded-xl">
                                <div className="p-1 bg-primary/20 rounded-lg">
                                  <CheckCircle className="h-3 w-3 text-primary" />
                                </div>
                                <span className="text-sm text-foreground">{feature}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm" className="hover:bg-muted/50">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm" className="hover:bg-muted/50">
                          <Terminal className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm" className="hover:bg-muted/50">
                          <Monitor className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

              <TabsContent value="repositories" className="space-y-6">
                <div className="flex justify-between items-center">
                  <h2 className="text-2xl font-semibold">Code Repositories</h2>
                  <Button size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Repository
                  </Button>
                </div>

                <div className="grid gap-6 md:grid-cols-2">
                  <Card className="border-0 shadow-sm">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-3">
                        <GitBranch className="h-5 w-5" />
                        main-frontend
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex justify-between text-sm">
                          <span>Last commit:</span>
                          <span className="text-muted-foreground">2 hours ago</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Active branches:</span>
                          <span className="text-muted-foreground">8</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Contributors:</span>
                          <span className="text-muted-foreground">5</span>
                        </div>
                        <div className="flex gap-2 pt-2">
                          <Button variant="outline" size="sm" className="flex-1">
                            <FileCode className="h-4 w-4 mr-2" />
                            Browse
                          </Button>
                          <Button variant="outline" size="sm" className="flex-1">
                            <GitBranch className="h-4 w-4 mr-2" />
                            Branches
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-0 shadow-sm">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-3">
                        <Database className="h-5 w-5" />
                        api-backend
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex justify-between text-sm">
                          <span>Last commit:</span>
                          <span className="text-muted-foreground">5 hours ago</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Active branches:</span>
                          <span className="text-muted-foreground">3</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Contributors:</span>
                          <span className="text-muted-foreground">3</span>
                        </div>
                        <div className="flex gap-2 pt-2">
                          <Button variant="outline" size="sm" className="flex-1">
                            <FileCode className="h-4 w-4 mr-2" />
                            Browse
                          </Button>
                          <Button variant="outline" size="sm" className="flex-1">
                            <GitBranch className="h-4 w-4 mr-2" />
                            Branches
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="ci-cd" className="space-y-6">
                <div className="flex justify-between items-center">
                  <h2 className="text-2xl font-semibold">CI/CD Pipeline</h2>
                  <Button size="sm">
                    <Settings className="h-4 w-4 mr-2" />
                    Configure
                  </Button>
                </div>

                <div className="grid gap-6 md:grid-cols-2">
                  <Card className="border-0 shadow-sm">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-3">
                        <Zap className="h-5 w-5 text-green-600" />
                        Build Pipeline
                        <Badge className="bg-green-100 text-green-800 border-green-200">
                          Healthy
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Success Rate (30 days)</span>
                          <span className="font-semibold">96.8%</span>
                        </div>
                        <Progress value={96.8} className="h-2" />
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-muted-foreground">Total Builds:</span>
                            <div className="font-semibold">124</div>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Avg Duration:</span>
                            <div className="font-semibold">3m 42s</div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-0 shadow-sm">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-3">
                        <Rocket className="h-5 w-5 text-blue-600" />
                        Deployment Pipeline
                        <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                          Active
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Success Rate (30 days)</span>
                          <span className="font-semibold">100%</span>
                        </div>
                        <Progress value={100} className="h-2" />
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-muted-foreground">Deployments:</span>
                            <div className="font-semibold">24</div>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Avg Duration:</span>
                            <div className="font-semibold">5m 18s</div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
      </div>
    </Layout>
  );
};

export default Development;