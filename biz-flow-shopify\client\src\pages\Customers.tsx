import { Layout } from "@/components/Layout";
import { PageContainer } from "@/components/layout/PageContainer";
import { PageHeader } from "@/components/layout/PageHeader";
import { DataTable } from "@/components/layout/DataTable";
import { StatusBadge } from "@/components/layout/StatusBadge";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { 
  Filter, 
  Download, 
  Eye, 
  MoreHorizontal,
  TrendingUp,
  Users,
  Crown,
  UserPlus,
  RefreshCw,
  Plus
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";

interface Customer {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  tier: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

export default function Customers() {
  const { data: customers = [], isLoading } = useQuery<Customer[]>({
    queryKey: ['/api/customers'],
  });
  
  // Ensure customers is always an array
  const safeCustomers = Array.isArray(customers) ? customers : [];

  const columns = [
    {
      key: "name",
      label: "Customer",
      render: (value: any, row: Customer) => (
        <div className="flex items-center gap-3">
          <Avatar className="w-8 h-8">
            <AvatarFallback className="text-xs font-medium">
              {row.firstName.charAt(0)}{row.lastName.charAt(0)}
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium">{row.firstName} {row.lastName}</div>
            <div className="text-sm text-muted-foreground">{row.email}</div>
          </div>
        </div>
      )
    },
    {
      key: "phone",
      label: "Phone",
      width: "140px",
      render: (value: string) => (
        <div className="text-sm">{value}</div>
      )
    },
    {
      key: "tier",
      label: "Tier",
      width: "100px",
      render: (value: string) => <StatusBadge status={value} />
    },
    {
      key: "status",
      label: "Status",
      width: "100px",
      render: (value: string) => <StatusBadge status={value} variant="compact" />
    },
    {
      key: "createdAt",
      label: "Joined",
      width: "120px",
      render: (value: string) => (
        <div className="text-sm">
          {new Date(value).toLocaleDateString()}
        </div>
      )
    },
    {
      key: "actions",
      label: "Actions",
      width: "100px",
      render: () => (
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm">
            <Eye className="w-4 h-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="w-4 h-4" />
          </Button>
        </div>
      )
    }
  ];

  return (
    <Layout>
      <PageContainer>
        <PageHeader 
          title="Customer Management"
          description="Manage customer profiles, preferences, and engagement"
          actions={
            <>
              <Button variant="outline" size="sm">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
              <Button variant="outline" size="sm">
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh
              </Button>
              <Button size="sm">
                <Plus className="w-4 h-4 mr-2" />
                Add Customer
              </Button>
            </>
          }
        />
        
        <div className="dashboard-section">
          {/* Customer Stats */}
          <div className="stats-grid">
            <Card className="metric-card">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 rounded-full bg-primary/10">
                    <Users className="w-5 h-5 text-primary" />
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-muted-foreground">Total Customers</p>
                    <p className="text-2xl font-bold">{safeCustomers.length}</p>
                    <div className="flex items-center gap-1 text-xs text-green-600">
                      <TrendingUp className="w-3 h-3" />
                      <span>+15%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="metric-card">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 rounded-full bg-purple-500/10">
                    <Crown className="w-5 h-5 text-purple-600" />
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-muted-foreground">VIP Customers</p>
                    <p className="text-2xl font-bold">
                      {safeCustomers.filter(customer => customer.tier === 'vip').length}
                    </p>
                    <p className="text-xs text-muted-foreground">Premium tier</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="metric-card">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 rounded-full bg-green-500/10">
                    <UserPlus className="w-5 h-5 text-green-600" />
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-muted-foreground">New This Month</p>
                    <p className="text-2xl font-bold">
                      {safeCustomers.filter(customer => {
                        const customerDate = new Date(customer.createdAt);
                        const now = new Date();
                        const monthAgo = new Date(now.getFullYear(), now.getMonth(), 1);
                        return customerDate >= monthAgo;
                      }).length}
                    </p>
                    <div className="flex items-center gap-1 text-xs text-green-600">
                      <TrendingUp className="w-3 h-3" />
                      <span>+22%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="metric-card">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 rounded-full bg-blue-500/10">
                    <Users className="w-5 h-5 text-blue-600" />
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-muted-foreground">Active Customers</p>
                    <p className="text-2xl font-bold">
                      {safeCustomers.filter(customer => customer.status === 'active').length}
                    </p>
                    <p className="text-xs text-muted-foreground">Last 30 days</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Customers Table */}
          <DataTable
            title="Customer Directory"
            description="View and manage all customer profiles"
            columns={columns}
            data={safeCustomers}
            emptyState={
              <div className="text-center py-8">
                <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No customers found</h3>
                <p className="text-muted-foreground text-sm">
                  Customer profiles will appear here once they sign up
                </p>
              </div>
            }
          />
        </div>
      </PageContainer>
    </Layout>
  );
}