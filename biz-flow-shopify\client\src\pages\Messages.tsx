import { useState } from "react";
import { MessageCircle, Send, Search, Filter, MoreVertical, Paperclip, Smile, Phone, Video, ArrowLeft } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { Layout } from "@/components/Layout";
import { Link } from "wouter";

const Messages = () => {
  const [selectedChat, setSelectedChat] = useState(0);
  const [newMessage, setNewMessage] = useState("");

  const conversations = [
    {
      id: 1,
      name: "<PERSON>",
      avatar: "SJ",
      lastMessage: "Thanks for the quick response! The new design looks amazing.",
      time: "2 min ago",
      unread: 2,
      status: "online"
    },
    {
      id: 2,
      name: "Marketing Team",
      avatar: "MT",
      lastMessage: "The campaign metrics are looking great this month.",
      time: "1 hour ago",
      unread: 0,
      status: "away"
    },
    {
      id: 3,
      name: "<PERSON>",
      avatar: "AC",
      lastMessage: "Can we schedule a call to discuss the project timeline?",
      time: "3 hours ago",
      unread: 1,
      status: "offline"
    }
  ];

  const messages = [
    { id: 1, sender: "Sarah Johnson", message: "Hi there! I love the new dashboard updates.", time: "10:30 AM", own: false },
    { id: 2, sender: "You", message: "Thank you! We've been working hard on improving the user experience.", time: "10:32 AM", own: true },
    { id: 3, sender: "Sarah Johnson", message: "The glassmorphism effects are really modern and sleek.", time: "10:35 AM", own: false },
    { id: 4, sender: "You", message: "We're glad you like it! Is there anything specific you'd like to see improved?", time: "10:37 AM", own: true },
    { id: 5, sender: "Sarah Johnson", message: "Thanks for the quick response! The new design looks amazing.", time: "10:40 AM", own: false }
  ];

  return (
    <Layout>
      <div className="h-[calc(100vh-4rem)] flex">
        {/* Sidebar */}
        <div className="w-80 border-r border-border/50 glass-card">
          {/* Header */}
          <div className="p-6 border-b border-border/50">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <Link to="/">
                  <Button variant="ghost" size="icon" className="hover:bg-accent/50">
                    <ArrowLeft className="h-4 w-4" />
                  </Button>
                </Link>
                <h1 className="text-2xl font-semibold bg-gradient-primary bg-clip-text text-transparent">
                  Messages
                </h1>
              </div>
              <Button variant="ghost" size="icon" className="hover:bg-accent/50">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </div>
            
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input 
                placeholder="Search conversations..." 
                className="pl-10 bg-background/50 backdrop-blur-sm border-border/50 focus:border-primary/50"
              />
            </div>
          </div>

          {/* Conversations */}
          <div className="flex-1 overflow-y-auto">
            {conversations.map((conversation, index) => (
              <div
                key={conversation.id}
                onClick={() => setSelectedChat(index)}
                className={`p-4 cursor-pointer transition-all duration-200 hover:bg-accent/30 border-l-2 ${
                  selectedChat === index ? 'bg-accent/50 border-primary' : 'border-transparent'
                }`}
              >
                <div className="flex items-start space-x-3">
                  <div className="relative">
                    <Avatar className="w-12 h-12">
                      <AvatarImage src="" />
                      <AvatarFallback className="bg-gradient-primary text-primary-foreground font-medium">
                        {conversation.avatar}
                      </AvatarFallback>
                    </Avatar>
                    <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-background ${
                      conversation.status === 'online' ? 'bg-success' : 
                      conversation.status === 'away' ? 'bg-warning' : 'bg-muted'
                    }`} />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h3 className="font-medium text-foreground truncate">{conversation.name}</h3>
                      <span className="text-xs text-muted-foreground">{conversation.time}</span>
                    </div>
                    <p className="text-sm text-muted-foreground truncate">{conversation.lastMessage}</p>
                  </div>
                  
                  {conversation.unread > 0 && (
                    <Badge variant="default" className="bg-primary text-primary-foreground ml-2">
                      {conversation.unread}
                    </Badge>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Chat Area */}
        <div className="flex-1 flex flex-col">
          {/* Chat Header */}
          <div className="p-6 border-b border-border/50 glass-card">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Avatar className="w-10 h-10">
                  <AvatarFallback className="bg-gradient-primary text-primary-foreground">
                    {conversations[selectedChat]?.avatar}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h2 className="font-semibold text-foreground">{conversations[selectedChat]?.name}</h2>
                  <p className="text-sm text-success">Active now</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Button variant="ghost" size="icon" className="hover:bg-accent/50">
                  <Phone className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="icon" className="hover:bg-accent/50">
                  <Video className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="icon" className="hover:bg-accent/50">
                  <Search className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="icon" className="hover:bg-accent/50">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-6 space-y-4">
            {messages.map((message) => (
              <div key={message.id} className={`flex ${message.own ? 'justify-end' : 'justify-start'}`}>
                <div className={`max-w-xs lg:max-w-md ${message.own ? 'order-2' : 'order-1'}`}>
                  <div className={`p-4 rounded-2xl ${
                    message.own 
                      ? 'bg-gradient-primary text-primary-foreground ml-4' 
                      : 'bg-card/80 backdrop-blur-sm border border-border/50 mr-4'
                  }`}>
                    <p className="text-sm">{message.message}</p>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1 px-4">{message.time}</p>
                </div>
                
                {!message.own && (
                  <Avatar className="w-8 h-8 order-1">
                    <AvatarFallback className="bg-muted text-xs">
                      {conversations[selectedChat]?.avatar}
                    </AvatarFallback>
                  </Avatar>
                )}
              </div>
            ))}
          </div>

          {/* Message Input */}
          <div className="p-6 border-t border-border/50 glass-card">
            <div className="flex items-end space-x-4">
              <div className="flex-1">
                <div className="flex items-center space-x-2 p-3 rounded-2xl bg-background/50 backdrop-blur-sm border border-border/50 focus-within:border-primary/50 transition-colors">
                  <Button variant="ghost" size="icon" className="h-8 w-8 hover:bg-accent/50">
                    <Paperclip className="h-4 w-4" />
                  </Button>
                  
                  <Input
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    placeholder="Type a message..."
                    className="border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0"
                    onKeyPress={(e) => e.key === 'Enter' && setNewMessage('')}
                  />
                  
                  <Button variant="ghost" size="icon" className="h-8 w-8 hover:bg-accent/50">
                    <Smile className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              <Button 
                onClick={() => setNewMessage('')}
                className="h-12 w-12 rounded-full bg-gradient-primary hover:opacity-90 transition-opacity"
                disabled={!newMessage.trim()}
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Messages;