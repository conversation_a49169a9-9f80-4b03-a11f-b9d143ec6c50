import { Layout } from "@/components/Layout";
import { PageContainer } from "@/components/layout/PageContainer";
import { PageHeader } from "@/components/layout/PageHeader";
import { DataTable } from "@/components/layout/DataTable";
import { StatusBadge } from "@/components/layout/StatusBadge";
import { MetricCard } from "@/components/dashboard/MetricCard";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Filter, 
  Download, 
  Eye, 
  MoreHorizontal,
  TrendingUp,
  Package,
  ShoppingCart,
  DollarSign,
  RefreshCw,
  Plus,
  AlertTriangle
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";

interface Product {
  id: number;
  name: string;
  description: string;
  price: number;
  category: string;
  status: string;
  stock: number;
  createdAt: string;
  updatedAt: string;
}

export default function Products() {
  const { data: products = [], isLoading } = useQuery<Product[]>({
    queryKey: ['/api/products'],
  });

  const columns = [
    {
      key: "name",
      label: "Product",
      render: (value: string, row: Product) => (
        <div className="space-y-1">
          <div className="font-medium">{value}</div>
          <div className="text-sm text-muted-foreground line-clamp-1">
            {row.description}
          </div>
        </div>
      )
    },
    {
      key: "category",
      label: "Category",
      width: "120px",
      render: (value: string) => (
        <Badge variant="secondary" className="text-xs">
          {value}
        </Badge>
      )
    },
    {
      key: "price",
      label: "Price",
      width: "100px",
      render: (value: number) => (
        <div className="font-medium">${value.toFixed(2)}</div>
      )
    },
    {
      key: "stock",
      label: "Stock",
      width: "80px",
      render: (value: number) => (
        <div className={`text-sm ${value <= 10 ? 'text-red-600' : 'text-foreground'}`}>
          {value}
        </div>
      )
    },
    {
      key: "status",
      label: "Status",
      width: "100px",
      render: (value: string) => <StatusBadge status={value} variant="compact" />
    },
    {
      key: "createdAt",
      label: "Created",
      width: "120px",
      render: (value: string) => (
        <div className="text-sm">
          {new Date(value).toLocaleDateString()}
        </div>
      )
    },
    {
      key: "actions",
      label: "Actions",
      width: "100px",
      render: () => (
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm">
            <Eye className="w-4 h-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="w-4 h-4" />
          </Button>
        </div>
      )
    }
  ];

  return (
    <Layout>
      <PageContainer>
        <PageHeader 
          title="Product Management"
          description="Manage your product catalog, inventory, and pricing"
          actions={
            <div className="flex gap-2">
              <button className="glass-button px-4 py-2 flex items-center gap-2 text-sm">
                <Filter className="w-4 h-4" />
                Filter
              </button>
              <button className="glass-button px-4 py-2 flex items-center gap-2 text-sm">
                <Download className="w-4 h-4" />
                Export
              </button>
              <button className="glass-button px-4 py-2 flex items-center gap-2 text-sm">
                <RefreshCw className="w-4 h-4" />
                Refresh
              </button>
              <button className="glass-button px-4 py-2 flex items-center gap-2 text-sm bg-primary/20 border-primary/30 text-primary">
                <Plus className="w-4 h-4" />
                Add Product
              </button>
            </div>
          }
        />
        
        <div className="dashboard-section">
          {/* Product Stats */}
          <div className="stats-grid">
            <MetricCard
              title="Total Products"
              value={products.length}
              change="+8%"
              changeType="positive"
              icon={<Package className="w-5 h-5" />}
            />
            
            <MetricCard
              title="Active Products"
              value={Array.isArray(products) ? products.filter(product => product.status === 'active').length : 0}
              change="+12%"
              changeType="positive"
              icon={<ShoppingCart className="w-5 h-5" />}
            />
            
            <MetricCard
              title="Low Stock Items"
              value={Array.isArray(products) ? products.filter(product => product.stock < 10).length : 0}
              change="-3%"
              changeType="negative"
              icon={<AlertTriangle className="w-5 h-5" />}
            />
            
            <MetricCard
              title="Avg. Product Price"
              value={`$${Array.isArray(products) && products.length > 0 
                ? (products.reduce((sum, p) => sum + p.price, 0) / products.length).toFixed(2) 
                : '0.00'}`}
              change="+5.2%"
              changeType="positive"
              icon={<DollarSign className="w-5 h-5" />}
            />
          </div>
          
          {/* Products Table */}
          <DataTable
            title="Product Catalog"
            description="View and manage all products in your inventory"
            columns={columns}
            data={products}
            emptyState={
              <div className="text-center py-8">
                <Package className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No products found</h3>
                <p className="text-muted-foreground text-sm">
                  Products will appear here once you add them to your catalog
                </p>
              </div>
            }
          />
        </div>
      </PageContainer>
    </Layout>
  );
}