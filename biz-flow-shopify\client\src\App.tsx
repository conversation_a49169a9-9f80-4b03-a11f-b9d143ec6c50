import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Router, Route, Switch } from "wouter";
import Index from "./pages/Index";
import Analytics from "./pages/Analytics";
import Development from "./pages/Development";
import ApiKeys from "./pages/ApiKeys";
import Products from "./pages/Products";
import Orders from "./pages/Orders";
import Customers from "./pages/Customers";
import Settings from "./pages/Settings";
import NotFound from "./pages/NotFound";
import WebsiteBuilder from "./pages/WebsiteBuilder";

import SupportCenter from "./pages/SupportCenter";
import Messages from "./pages/Messages";
import AccessibilityChecker from "./pages/AccessibilityChecker";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <Router>
        <Switch>
          <Route path="/" component={Index} />
          <Route path="/website-builder" component={WebsiteBuilder} />

          <Route path="/products" component={Products} />
          <Route path="/orders" component={Orders} />
          <Route path="/customers" component={Customers} />
          <Route path="/analytics" component={Analytics} />
          <Route path="/development" component={Development} />
          <Route path="/api-keys" component={ApiKeys} />
          <Route path="/accessibility" component={AccessibilityChecker} />
          <Route path="/support" component={SupportCenter} />
          <Route path="/settings" component={Settings} />
          <Route path="/messages" component={Messages} />
          <Route component={NotFound} />
        </Switch>
      </Router>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
