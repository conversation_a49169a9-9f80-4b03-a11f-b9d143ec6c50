import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Eye, MoreHorizontal } from "lucide-react";

const recentOrders = [
  {
    id: "#3210",
    customer: "<PERSON> Johnson",
    email: "<EMAIL>",
    product: "Premium Dashboard Template",
    amount: "$89.00",
    status: "completed",
    date: "2 hours ago"
  },
  {
    id: "#3209",
    customer: "<PERSON>",
    email: "<EMAIL>",
    product: "E-commerce Starter Kit",
    amount: "$156.00",
    status: "processing",
    date: "5 hours ago"
  },
  {
    id: "#3208",
    customer: "<PERSON>",
    email: "<EMAIL>",
    product: "Mobile App UI Kit",
    amount: "$67.00",
    status: "completed",
    date: "1 day ago"
  },
  {
    id: "#3207",
    customer: "<PERSON>",
    email: "<EMAIL>",
    product: "SaaS Landing Page",
    amount: "$234.00",
    status: "pending",
    date: "2 days ago"
  },
];

const getStatusColor = (status: string) => {
  switch (status) {
    case "completed":
      return "bg-success/10 text-success border-success/20";
    case "processing":
      return "bg-warning/10 text-warning border-warning/20";
    case "pending":
      return "bg-muted text-muted-foreground border-muted";
    default:
      return "bg-muted text-muted-foreground border-muted";
  }
};

export function RecentOrders() {
  return (
    <Card className="shadow-card border-0">
      <CardHeader className="pb-4">
        <CardTitle className="text-xl font-semibold">Recent Orders</CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          {recentOrders.map((order) => (
            <div key={order.id} className="flex items-center justify-between p-4 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <span className="font-semibold text-primary text-sm">{order.id}</span>
                  <Badge variant="secondary" className={getStatusColor(order.status)}>
                    {order.status}
                  </Badge>
                </div>
                <div className="text-sm text-muted-foreground space-y-1">
                  <div className="font-medium text-foreground text-sm">{order.customer}</div>
                  <div className="text-xs">{order.product}</div>
                </div>
              </div>
              <div className="text-right mr-3">
                <div className="font-semibold text-lg">{order.amount}</div>
                <div className="text-xs text-muted-foreground">{order.date}</div>
              </div>
              <div className="flex gap-2">
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Eye className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}