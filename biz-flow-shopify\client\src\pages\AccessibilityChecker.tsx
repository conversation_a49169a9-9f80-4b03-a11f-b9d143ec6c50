import { useState, useEffect } from "react";
import { Layout } from "@/components/Layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  <PERSON>lette, 
  Eye, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Copy,
  Shuffle,
  RefreshCw
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// Color utility functions
const hexToRgb = (hex: string) => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
};

const getLuminance = (rgb: { r: number; g: number; b: number }) => {
  const { r, g, b } = rgb;
  const sRGB = [r, g, b].map(c => {
    c = c / 255;
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
  });
  return 0.2126 * sRGB[0] + 0.7152 * sRGB[1] + 0.0722 * sRGB[2];
};

const getContrastRatio = (color1: string, color2: string) => {
  const rgb1 = hexToRgb(color1);
  const rgb2 = hexToRgb(color2);
  
  if (!rgb1 || !rgb2) return 0;
  
  const lum1 = getLuminance(rgb1);
  const lum2 = getLuminance(rgb2);
  
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);
  
  return (brightest + 0.05) / (darkest + 0.05);
};

const getWCAGLevel = (ratio: number, isLargeText: boolean = false) => {
  if (isLargeText) {
    if (ratio >= 4.5) return { level: 'AAA', color: 'success' };
    if (ratio >= 3) return { level: 'AA', color: 'success' };
    return { level: 'FAIL', color: 'destructive' };
  } else {
    if (ratio >= 7) return { level: 'AAA', color: 'success' };
    if (ratio >= 4.5) return { level: 'AA', color: 'success' };
    return { level: 'FAIL', color: 'destructive' };
  }
};

interface ColorPalette {
  name: string;
  colors: { name: string; hex: string; description: string }[];
}

const designSystemColors: ColorPalette[] = [
  {
    name: "Primary Colors",
    colors: [
      { name: "Primary", hex: "#3b82f6", description: "Main brand color" },
      { name: "Primary Light", hex: "#93c5fd", description: "Light variant" },
      { name: "Primary Dark", hex: "#1e40af", description: "Dark variant" },
    ]
  },
  {
    name: "Neutrals",
    colors: [
      { name: "Background", hex: "#f8fafc", description: "Main background" },
      { name: "Surface", hex: "#ffffff", description: "Card backgrounds" },
      { name: "Border", hex: "#e2e8f0", description: "Borders and dividers" },
      { name: "Text Primary", hex: "#1e293b", description: "Primary text" },
      { name: "Text Secondary", hex: "#64748b", description: "Secondary text" },
      { name: "Text Muted", hex: "#94a3b8", description: "Muted text" },
    ]
  },
  {
    name: "Status Colors",
    colors: [
      { name: "Success", hex: "#10b981", description: "Success states" },
      { name: "Warning", hex: "#f59e0b", description: "Warning states" },
      { name: "Error", hex: "#ef4444", description: "Error states" },
      { name: "Info", hex: "#3b82f6", description: "Info states" },
    ]
  }
];

export default function AccessibilityChecker() {
  const [foregroundColor, setForegroundColor] = useState("#1e293b");
  const [backgroundColor, setBackgroundColor] = useState("#ffffff");
  const [contrastRatio, setContrastRatio] = useState(0);
  const [isLargeText, setIsLargeText] = useState(false);
  const [selectedPalette, setSelectedPalette] = useState<ColorPalette>(designSystemColors[0]);
  const { toast } = useToast();

  useEffect(() => {
    const ratio = getContrastRatio(foregroundColor, backgroundColor);
    setContrastRatio(ratio);
  }, [foregroundColor, backgroundColor]);

  const wcagLevel = getWCAGLevel(contrastRatio, isLargeText);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied to clipboard",
      description: `Color ${text} copied successfully`,
    });
  };

  const generateRandomColor = () => {
    return "#" + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0');
  };

  const swapColors = () => {
    const temp = foregroundColor;
    setForegroundColor(backgroundColor);
    setBackgroundColor(temp);
  };

  const testColorCombination = (fg: string, bg: string) => {
    setForegroundColor(fg);
    setBackgroundColor(bg);
  };

  const getStatusIcon = (level: string) => {
    switch (level) {
      case 'AAA':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'AA':
        return <CheckCircle className="w-4 h-4 text-blue-500" />;
      default:
        return <XCircle className="w-4 h-4 text-red-500" />;
    }
  };

  return (
    <Layout>
      <div className="p-8 max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-3">
            <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
              <Eye className="w-4 h-4 text-primary-foreground" />
            </div>
            <h1 className="text-3xl font-bold bg-gradient-primary bg-clip-text text-transparent">
              Accessibility Color Contrast Checker
            </h1>
          </div>
          <p className="text-muted-foreground">
            Ensure your color combinations meet WCAG accessibility standards
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Color Input Panel */}
          <div className="lg:col-span-1">
            <Card className="glass-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Palette className="w-5 h-5" />
                  Color Selection
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Foreground Color */}
                <div className="space-y-2">
                  <Label htmlFor="foreground">Foreground Color (Text)</Label>
                  <div className="flex gap-2">
                    <Input
                      id="foreground"
                      type="color"
                      value={foregroundColor}
                      onChange={(e) => setForegroundColor(e.target.value)}
                      className="w-16 h-10 p-1 border-2"
                    />
                    <Input
                      value={foregroundColor}
                      onChange={(e) => setForegroundColor(e.target.value)}
                      className="flex-1"
                      placeholder="#000000"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(foregroundColor)}
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {/* Background Color */}
                <div className="space-y-2">
                  <Label htmlFor="background">Background Color</Label>
                  <div className="flex gap-2">
                    <Input
                      id="background"
                      type="color"
                      value={backgroundColor}
                      onChange={(e) => setBackgroundColor(e.target.value)}
                      className="w-16 h-10 p-1 border-2"
                    />
                    <Input
                      value={backgroundColor}
                      onChange={(e) => setBackgroundColor(e.target.value)}
                      className="flex-1"
                      placeholder="#ffffff"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(backgroundColor)}
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={swapColors}
                    className="flex-1"
                  >
                    <Shuffle className="w-4 h-4 mr-2" />
                    Swap
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setForegroundColor(generateRandomColor());
                      setBackgroundColor(generateRandomColor());
                    }}
                    className="flex-1"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Random
                  </Button>
                </div>

                {/* Text Size Toggle */}
                <div className="space-y-2">
                  <Label>Text Size</Label>
                  <Select value={isLargeText ? "large" : "normal"} onValueChange={(value) => setIsLargeText(value === "large")}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="normal">Normal Text (14px+)</SelectItem>
                      <SelectItem value="large">Large Text (18px+ or 14px+ bold)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Preview and Results */}
          <div className="lg:col-span-2 space-y-6">
            {/* Contrast Results */}
            <Card className="glass-card">
              <CardHeader>
                <CardTitle>Contrast Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary">
                      {contrastRatio.toFixed(2)}:1
                    </div>
                    <div className="text-sm text-muted-foreground">Contrast Ratio</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-2">
                      {getStatusIcon(wcagLevel.level)}
                      <Badge variant={wcagLevel.color === 'success' ? 'default' : 'destructive'}>
                        {wcagLevel.level}
                      </Badge>
                    </div>
                    <div className="text-sm text-muted-foreground mt-1">WCAG Level</div>
                  </div>

                  <div className="text-center">
                    <div className="flex items-center justify-center gap-2">
                      {wcagLevel.level !== 'FAIL' ? (
                        <CheckCircle className="w-5 h-5 text-green-500" />
                      ) : (
                        <XCircle className="w-5 h-5 text-red-500" />
                      )}
                      <span className="font-medium">
                        {wcagLevel.level !== 'FAIL' ? 'Pass' : 'Fail'}
                      </span>
                    </div>
                    <div className="text-sm text-muted-foreground mt-1">
                      {isLargeText ? 'Large Text' : 'Normal Text'}
                    </div>
                  </div>
                </div>

                {/* WCAG Guidelines */}
                <div className="mt-6 p-4 bg-muted/50 rounded-lg">
                  <h4 className="font-semibold mb-2">WCAG Guidelines:</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="font-medium">Normal Text:</div>
                      <div>AA: 4.5:1 minimum</div>
                      <div>AAA: 7:1 minimum</div>
                    </div>
                    <div>
                      <div className="font-medium">Large Text:</div>
                      <div>AA: 3:1 minimum</div>
                      <div>AAA: 4.5:1 minimum</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Preview */}
            <Card className="glass-card">
              <CardHeader>
                <CardTitle>Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <div 
                  className="p-6 rounded-lg border-2 border-dashed border-border"
                  style={{ backgroundColor: backgroundColor }}
                >
                  <div style={{ color: foregroundColor }}>
                    <h2 className={`font-bold mb-4 ${isLargeText ? 'text-2xl' : 'text-lg'}`}>
                      Sample Heading
                    </h2>
                    <p className={`mb-4 ${isLargeText ? 'text-lg' : 'text-base'}`}>
                      This is a sample paragraph to demonstrate how the selected colors look together. 
                      The contrast ratio helps ensure that text is readable for all users, including 
                      those with visual impairments.
                    </p>
                    <div className="flex gap-4">
                      <span className="underline cursor-pointer">Sample Link</span>
                      <span className="font-semibold">Bold Text</span>
                      <span className="italic">Italic Text</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Design System Color Palettes */}
        <div className="mt-8">
          <Card className="glass-card">
            <CardHeader>
              <CardTitle>Design System Colors</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="primary" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  {designSystemColors.map((palette) => (
                    <TabsTrigger key={palette.name} value={palette.name.toLowerCase().replace(' ', '-')}>
                      {palette.name}
                    </TabsTrigger>
                  ))}
                </TabsList>
                
                {designSystemColors.map((palette) => (
                  <TabsContent key={palette.name} value={palette.name.toLowerCase().replace(' ', '-')}>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {palette.colors.map((color) => (
                        <Card key={color.name} className="cursor-pointer hover:shadow-lg transition-shadow">
                          <CardContent className="p-4">
                            <div className="flex items-center gap-3 mb-3">
                              <div 
                                className="w-12 h-12 rounded-lg border-2 border-border"
                                style={{ backgroundColor: color.hex }}
                              />
                              <div>
                                <div className="font-semibold">{color.name}</div>
                                <div className="text-sm text-muted-foreground">{color.hex}</div>
                              </div>
                            </div>
                            <div className="text-xs text-muted-foreground mb-3">
                              {color.description}
                            </div>
                            <div className="flex gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => testColorCombination(color.hex, backgroundColor)}
                                className="flex-1"
                              >
                                Test as Text
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => testColorCombination(foregroundColor, color.hex)}
                                className="flex-1"
                              >
                                Test as Background
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </TabsContent>
                ))}
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  );
}